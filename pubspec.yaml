name: card_promo
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.3.4 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0
  get: ^4.6.5
  dio: ^5.1.2
  http: ^0.13.5
  connectivity: ^3.0.6
  flutter_secure_storage: ^8.0.0
  flutter_native_splash: ^2.2.10
  audioplayers: ^4.1.0
  url_launcher: ^6.1.2
  google_mobile_ads: ^4.0.0
  flutter_svg: ^1.1.6
  webview_flutter: ^4.2.1
  flutter_facebook_auth: ^6.0.4
  shared_preferences: ^2.3.3
  text_scroll: ^0.2.0

  flutter_swipe: ^1.0.1
  google_maps_flutter: ^2.10.1
  loading_animation_widget: ^1.3.0
  location: ^6.0.0
  screenshot: ^3.0.0
  share_plus: ^10.1.4
  path_provider: ^2.1.2

#  basic_utils: ^3.5.0
  pointycastle: ^3.7.3
  encrypt: ^5.0.1

  cryptography: ^2.7.0

  sliding_up_panel: ^2.0.0+1

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - lib/res/image/
    - lib/res/string/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Roboto-Thin
      fonts:
        - asset: lib/res/font/Roboto-Thin.ttf
    - family: Roboto-Regular
      fonts:
        - asset: lib/res/font/Roboto-Regular.ttf
    - family: Roboto-Medium
      fonts:
        - asset: lib/res/font/Roboto-Medium.ttf
    - family: Roboto-Bold
      fonts:
        - asset: lib/res/font/Roboto-Bold.ttf
    - family: Roboto-Italic
      fonts:
        - asset: lib/res/font/Roboto-Italic.ttf
    - family: Roboto-Light
      fonts:
        - asset: lib/res/font/Roboto-Light.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

flutter_native_splash:
  color: "#ffffff"
  image: lib/res/image/1024.png
  android: true
  ios: true
  web: true