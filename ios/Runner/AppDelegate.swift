import Flutter
import UIKit
import GoogleMaps
import <PERSON>BSDKCoreKit
import <PERSON>BS<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GMSServices.provideAPIKey("AIzaSyAre5vh4ePDR0RsiWH_LWLmlDPy4fXB5XM")
    GeneratedPluginRegistrant.register(with: self)
    
    // Khởi tạo Facebook SDK
    ApplicationDelegate.shared.application(
      application,
      didFinishLaunchingWithOptions: launchOptions
    )
    
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  override func application(
          _ app: UIApplication,
          open url: URL,
          options: [UIApplication.OpenURLOptionsKey : Any] = [:]
      ) -> Bool {
          ApplicationDelegate.shared.application(
              app,
              open: url,
              sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
              annotation: options[UIApplication.OpenURLOptionsKey.annotation]
          )
      }
      
  // Thêm phương thức này để xử lý URL scheme
  override func application(
    _ application: UIApplication,
    open url: URL,
    sourceApplication: String?,
    annotation: Any
  ) -> Bool {
    return ApplicationDelegate.shared.application(
      application,
      open: url,
      sourceApplication: sourceApplication,
      annotation: annotation
    )
  }
}
