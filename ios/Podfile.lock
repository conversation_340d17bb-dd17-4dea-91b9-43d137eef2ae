PODS:
  - audioplay<PERSON>_da<PERSON>win (0.0.1):
    - Flutter
  - connectivity (0.0.1):
    - Flutter
    - Reachability
  - FBAEMKit (16.3.1):
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit (16.3.1):
    - FBAEMKit (= 16.3.1)
    - FBSDKCoreKit_Basics (= 16.3.1)
  - FBSDKCoreKit_Basics (16.3.1)
  - FBSDKLoginKit (16.3.1):
    - FBSDKCoreKit (= 16.3.1)
  - Flutter (1.0.0)
  - flutter_facebook_auth (6.0.4):
    - FBSDKLoginKit (~> 16.3.1)
    - Flutter
  - flutter_native_splash (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - Google-Mobile-Ads-SDK (10.11.0):
    - GoogleAppMeasurement (< 11.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - google_mobile_ads (1.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 10.11.0)
    - webview_flutter_wkwebview
  - GoogleAppMeasurement (10.29.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.29.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.29.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.29.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - GoogleUserMessagingPlatform (3.0.0)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - location (0.0.1):
    - Flutter
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - Reachability (3.7.6)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - connectivity (from `.symlinks/plugins/connectivity/ios`)
  - FBSDKCoreKit
  - FBSDKLoginKit
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - Google-Maps-iOS-Utils
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - GoogleMaps
  - location (from `.symlinks/plugins/location/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Google-Maps-iOS-Utils
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleMaps
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - Reachability

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  connectivity:
    :path: ".symlinks/plugins/connectivity/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: ccf9c770ee768abb07e26d90af093f7bab1c12ab
  connectivity: e7d9fd79099f39025737062855c00248b7afcdf0
  FBAEMKit: 6c7b5eb77c96861bb59e040842c6e55bf39512ce
  FBSDKCoreKit: 5e4dd478947ab1bcc887e8cfadeae0727af1a942
  FBSDKCoreKit_Basics: cd7b5f5d1e8868c26706917919d058999ca672c3
  FBSDKLoginKit: 572cca0bc6c90067ef197187697cb3b584310c52
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_facebook_auth: 81e624a905d30b1f97ebffb8b2feaf9e5330537a
  flutter_native_splash: 35ddbc7228eafcb3969dcc5f1fbbe27c1145a4f0
  flutter_secure_storage: 2c2ff13db9e0a5647389bff88b0ecac56e3f3418
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  Google-Mobile-Ads-SDK: 58b4fda3f9758fc1ed210aa5cf7777b5eb55d47e
  google_maps_flutter_ios: 0291eb2aa252298a769b04d075e4a9d747ff7264
  google_mobile_ads: c49bca7ce6656c411965e56b4e24893466fef840
  GoogleAppMeasurement: f9de05ee17401e3355f68e8fc8b5064d429f5918
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  GoogleUserMessagingPlatform: f8d0cdad3ca835406755d0a69aa634f00e76d576
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  location: 155caecf9da4f280ab5fe4a55f94ceccfab838f8
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  Reachability: fd0ecd23705e2599e4cceeb943222ae02296cbc6
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  webview_flutter_wkwebview: 45a041c7831641076618876de3ba75c712860c6b

PODFILE CHECKSUM: 94c787d03e501fb4ffd4d9904b61312575bcb51d

COCOAPODS: 1.16.2
