enum Environment { DEV, LIVE, PROD }

class BuildConstants {
  static late Map<String, dynamic> _config;
  static var currentEnvironment = Environment.DEV;

  static void setEnvironment(Environment env) {
    switch (env) {
      case Environment.PROD:
        _config = _Config.prodConstants;
        currentEnvironment = Environment.PROD;
        break;
      case Environment.LIVE:
        _config = _Config.prodConstants;
        currentEnvironment = Environment.LIVE;
        break;
      case Environment.DEV:
        _config = _Config.devConstants;
        currentEnvironment = Environment.DEV;
        break;
    }
  }

  static get serverAPI {
    return _config[_Config.SERVER_API];
  }

  static get appTYPE {
    return _config[_Config.APP_TYPE];
  }
}

class _Config {
  static const SERVER_API = "SERVER_API";
  static const APP_TYPE = "APP_TYPE";

  static Map<String, dynamic> prodConstants = {
    SERVER_API: "http://34.143.232.9:8080",
    APP_TYPE: "CardPromo",
  };

  static Map<String, dynamic> devConstants = {
    SERVER_API: "http://10.5.13.119:8080",
    // SERVER_API: "http://10.5.13.84:8080",
    // SERVER_API: "http://192.168.1.3:8080",
    // SERVER_API: "http://59.153.238.210:8080",
    APP_TYPE: "CardPromo dev",
  };
}
