import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import 'app_binding.dart';
import 'build_constants.dart';
import 'res/string/app_strings.dart';
import 'root_screen.dart';
import 'util/app_route.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  final config = RequestConfiguration(
    testDeviceIds: ['8F44B427BC6FF8FC9F63CFC9E7025A73'], // Thay bằng ID bạn lấy được
  );
  MobileAds.instance.updateRequestConfiguration(config);

  BuildConstants.setEnvironment(Environment.PROD);
  MobileAds.instance.initialize();

  runApp(GetMaterialApp(
    debugShowCheckedModeBanner: false,
    initialBinding: AppBinding(),
    initialRoute: AppNameRoute.home_screen,
    getPages: AppRoute.pages,
    theme: ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primary),
      fontFamily: kFontFamilyBeVietnamPro,
    ),
    localizationsDelegates: const [
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
    ],
    translations: AppStrings(),
    supportedLocales: [const Locale('vi', 'VN'), const Locale('en', 'US')],
    locale: const Locale('vi', 'VN'),
    fallbackLocale: const Locale('vi', 'VN'),
    builder: (context, child) {
      return RootScreen(child);
    },
  ));
}
