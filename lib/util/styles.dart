import 'package:flutter/material.dart';

import '../res/color/app_colors.dart';

const kFontFamilyBeVietnamPro = "BeVietnamPro";

// Size 30
const style_S30_W600_BlackColor = TextStyle(
    fontSize: 30,
    color: AppColors.blackText,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S30_W600_BlueColor = TextStyle(
    fontSize: 30,
    color: AppColors.blueText,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S30_W400_BlackColor = TextStyle(
    fontSize: 30,
    color: AppColors.blackText,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S24_W600_BlackColor = TextStyle(
    fontSize: 24,
    color: AppColors.blackText,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S24_W600_WhiteColor = TextStyle(
    fontSize: 24,
    color: AppColors.white,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);


// Size 20

const style_S20_W600_WhiteColor = TextStyle(
    fontSize: 20,
    color: AppColors.white,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S20_W600_BlueColor = TextStyle(
    fontSize: 20,
    color: AppColors.blueText,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S20_W600_BlackColor = TextStyle(
    fontSize: 20,
    color: AppColors.blackText,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S20_W400_WhiteColor = TextStyle(
    fontSize: 20,
    color: AppColors.white,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S20_W400_BlackColor = TextStyle(
    fontSize: 20,
    color: AppColors.blackText,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro);

// Size 16
const style_S16_W400_BlackColor = TextStyle(
    fontSize: 16,
    color: AppColors.blackText,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S16_W400_BlueColor = TextStyle(
    fontSize: 16,
    color: AppColors.success,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S16_W400_RedColor = TextStyle(
    fontSize: 16,
    color: AppColors.redText,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S16_W400_WhiteColor = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.white,);

const style_S16_W600_WhiteColor = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.white);

const style_S16_W600_BlackColor = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blackText);

const style_S16_W600_BlueColor = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blueText,);
// Size 18

const style_S18_W600_WhiteColor = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.white);

const style_S18_W600_RedColor = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.red);

const style_S18_W600_BlackColor = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blackText);

const style_S18_W400_BlackColor = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blackText);

// Size 14
const style_S14_W400_WhiteColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: AppColors.white,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S14_W600_WhiteColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    color: AppColors.white,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S14_W600_BlackColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blackText);

const style_S14_W600_RedColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.ufo_red);

const style_S14_W400_BlueColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blueText);

const style_S14_W600_BlueColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blueText);

const style_S14_W600_MainColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.main_background);

const style_S14_W400_BlackColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.blackText);

const style_S14_W400_GreyColor = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    fontFamily: kFontFamilyBeVietnamPro,
    color: AppColors.greyTextContent);

const style_S12_W600_WhiteColor = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.white,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S12_W600_BlackColor = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.blackText,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S12_W600_WarningColor = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w600,
    color: AppColors.warning,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S12_W400_BlackColor = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: AppColors.blackText,
    fontFamily: kFontFamilyBeVietnamPro);

const style_S10_W600_WhiteColor = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w600,
    color: AppColors.white,
    fontFamily: kFontFamilyBeVietnamPro);