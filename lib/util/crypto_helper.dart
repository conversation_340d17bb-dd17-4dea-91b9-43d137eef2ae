import 'dart:convert';
import 'dart:typed_data';
import 'dart:math';

import 'package:cryptography/cryptography.dart';
import 'package:encrypt/encrypt.dart';
// import 'package:pointycastle/export.dart';
import 'package:pointycastle/export.dart' hide Mac;
import 'package:pointycastle/export.dart' as pc; // Use 'pc' prefix for pointycastle

// import 'local_storage.dart';

/// A simple class to wrap the encrypted data similar to DataBaseObj in Java.
class DataBaseObj {
  String data;
  DataBaseObj({required this.data});

  factory DataBaseObj.fromJson(Map<String, dynamic> json) {
    return DataBaseObj(data: json['data'] ?? '');
  }

  Map<String, dynamic> toJson() {
    return {'data': data};
  }
}

/// A singleton that provides encryption/decryption functionalities similar to the Java CryptoInterface.
///
/// This implementation uses AES-GCM (with a 256-bit key) for symmetric encryption and RSA with OAEP for
/// “public key” encryption.
class CryptoInterface {
  // Singleton instance
  static final CryptoInterface _instance = CryptoInterface._internal();
  factory CryptoInterface() => _instance;
  CryptoInterface._internal() {
    // Initialize Aead (AES-GCM) key.
    _initAead();
  }

  // Shared preferences keys
  static const String _keysetName = 'ksHandle';
  static const String _prefFileName = 'prefMposTink'; // not used directly (for clarity)
  // static const String _pk = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0NCk1JSUNJakFOQmdrcWhraUc5dzBCQVFFRkFBT0NBZzhBTUlJQ0NnS0NBZ0VBenBpRHJtL010TGh1RlpManpqSnANCkU4cSthT0NTUFpzZDlVN3E5YWwvNVVUeU9zUytBSGxldDBUL1NYVnBSaDhBQ0taYkc2elNMSFRHdFU2TFF2RWkNCjRqL0NuQ3k3K2NMb3VDaERTU3pMZlNmaWRzRUVqdUFNTklpVWFSREtyaGk5VXplK1pEbnA2TGVpcEdMWitnRFYNCmFsa3NMcDlhc2hlc013dVBqbC9wRDdhQUxqNzBrbUFnWXFQanNvN1Y3V290VGhwbkthMWI1Qk5Ga1p5NFZMRmINCmkrSlNuNTgxK3hIazBtRDhTYlZLUi9wbFlNaDRMeGkvRmZvdFF4SUdLOE40RzRzYkMrRmZDMXBVRjdxVXl6djMNCkpwNmJSeVBLUG8wMGVXWEFGRTRlbmsyRk9qYlNUcnJNa1VVa3hIUFF2bjYvLzArMWRHVEtkaWl3Mkg4UHkxSm0NCmUwQVpiUkd1Z1dEQ2kwRHhMdFcvYzJuVTR0QXFkbDBCSWdtd04xdDRubzlWQ3hsa0c5dHloQ2lGQVUrVGR1c00NCjNXOFdEOC9wYUxwc3ZtSWtKcVNhN1Fsdk4rMTc2cU5ndW4zK1NHcGxpbS83b0dMOFRxU3ZhZ2pzQlNCaG44VWENCjdCbFQ0d1pNMjVkTHJINTY3Y0phVFgwYkJ6a01EUmN5cWZRbnBGSkhPWWwzSEtqa0xqRjNzbUFwanJsdUV4RnYNCkFuRCs2QlZwY3VqTzlXdFhBeWN4QVgzcnd0Q1VQK0dMQlM5aXpSVld3dTQzZEpCbGVsaWpFYTRJem45K1ZuS0gNCi9ETVZHS0tDNzZTaDI1K3RkcmcvbHZOalF1bm9ZRjBPajhiUGRhamNmVmNmRlVZVHpOQXdPSFpxc3R1YU1ocXENCmRYdHR0dUFzckRvaGdsWFhzL2Q5Ym9VQ0F3RUFBUT09DQotLS0tLUVORCBQVUJMSUMgS0VZLS0tLS0="; // placeholder public key in base64
  static const String _pk = "-----BEGIN PUBLIC KEY----- MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAzpiDrm/MtLhuFZLjzjJp E8q+aOCSPZsd9U7q9al/5UTyOsS+AHlet0T/SXVpRh8ACKZbG6zSLHTGtU6LQvEi 4j/CnCy7+cLouChDSSzLfSfidsEEjuAMNIiUaRDKrhi9Uze+ZDnp6LeipGLZ+gDV alksLp9ashesMwuPjl/pD7aALj70kmAgYqPjso7V7WotThpnKa1b5BNFkZy4VLFb i+JSn581+xHk0mD8SbVKR/plYMh4Lxi/FfotQxIGK8N4G4sbC+FfC1pUF7qUyzv3 Jp6bRyPKPo00eWXAFE4enk2FOjbSTrrMkUUkxHPQvn6//0+1dGTKdiiw2H8Py1Jm e0AZbRGugWDCi0DxLtW/c2nU4tAqdl0BIgmwN1t4no9VCxlkG9tyhCiFAU+TdusM 3W8WD8/paLpsvmIkJqSa7QlvN+176qNgun3+SGplim/7oGL8TqSvagjsBSBhn8Ua 7BlT4wZM25dLrH567cJaTX0bBzkMDRcyqfQnpFJHOYl3HKjkLjF3smApjrluExFv AnD+6BVpcujO9WtXAycxAX3rwtCUP+GLBS9izRVWwu43dJBlelijEa4Izn9+VnKH /DMVGKKC76Sh25+tdrg/lvNjQunoYF0Oj8bPdajcfVcfFUYTzNAwOHZqstuaMhqq dXtttuAsrDohglXXs/d9boUCAwEAAQ== -----END PUBLIC KEY-----";
  static const String _pvk = '-----BEGIN RSA PRIVATE KEY----- MIIJKQIBAAKCAgEAzpiDrm/MtLhuFZLjzjJpE8q+aOCSPZsd9U7q9al/5UTyOsS+ AHlet0T/SXVpRh8ACKZbG6zSLHTGtU6LQvEi4j/CnCy7+cLouChDSSzLfSfidsEE juAMNIiUaRDKrhi9Uze+ZDnp6LeipGLZ+gDValksLp9ashesMwuPjl/pD7aALj70 kmAgYqPjso7V7WotThpnKa1b5BNFkZy4VLFbi+JSn581+xHk0mD8SbVKR/plYMh4 Lxi/FfotQxIGK8N4G4sbC+FfC1pUF7qUyzv3Jp6bRyPKPo00eWXAFE4enk2FOjbS TrrMkUUkxHPQvn6//0+1dGTKdiiw2H8Py1Jme0AZbRGugWDCi0DxLtW/c2nU4tAq dl0BIgmwN1t4no9VCxlkG9tyhCiFAU+TdusM3W8WD8/paLpsvmIkJqSa7QlvN+17 6qNgun3+SGplim/7oGL8TqSvagjsBSBhn8Ua7BlT4wZM25dLrH567cJaTX0bBzkM DRcyqfQnpFJHOYl3HKjkLjF3smApjrluExFvAnD+6BVpcujO9WtXAycxAX3rwtCU P+GLBS9izRVWwu43dJBlelijEa4Izn9+VnKH/DMVGKKC76Sh25+tdrg/lvNjQuno YF0Oj8bPdajcfVcfFUYTzNAwOHZqstuaMhqqdXtttuAsrDohglXXs/d9boUCAwEA AQKCAgAhr6CZCsW4GfJpNiBK+NFFVuOhOizeOrKx0wJguBiQi6NrjbTlYuTcwTVX Bvv5YQiiYG2un4lNkmTsHzh529OyTG8xA+JKoUtM69O81L8BU0WL04EowsN6tWJt lz1VEdedABPdR8wqE/dDEpM0PnqN8qhPmdAyBcYgNJxdvKnF1B4mbEF4tI/TRaHU 9ZFQOuXUPF3h39CB6dqIGaO8u4aP0oFiYZVtsMc8vm+knRjVj7CEDWv7Ue+mZ7qF myTCcgbdhtsMHt1efoYDsHqegNKVy3rKKL3LYT7TJJIjPXGkokM88L9t8ut0LHAI UFITHZu0x4xwxBZ3Gb9sLZunrXHkD9aTLNiEeT5n6vHoGJueHXzVtPJ2Hy7P1+sP dV1SJHaISDGmL8hNSnqhBsU8jfNE4U28qEd/dBoyH7OB5A7p4ACpmSnbiBBp3fis GOmVxlcQl72AVdQfSMlmH6EI42TIEvvQ0+WCG/vY6ukZUAUtXy0gLh+UreU+yZKV eZ0bR0O8paJqgG1+LIDSXpbCCOnfDoGDzEAT+RdQvvdPzpvPkEya7f/KocT0B6bq QyrT4BYXrh3k/DsqtrKdB8PFDpUH34guDV2NQlRRpSn8LmYbMIIRjf9TY6YrY2lD d/JAaao2A7b9+bCIyZAXVdDdU9Tp4yDQ5qT1DJnPqdhKkPjbgQKCAQEA6obWRoIS 16BYtHn0DHmZMpkS/PZ0EQ2rxfAab/9c2QGh5/HyKqkcDOB5k+IirR3EPFcGfza0 kc+WP1KytvExgDKZZc5B55jUKt5SrlMqV3aJsMNk6zxC7+Wk9cNT+8nLcDPzJYAT AgoIHQeyEwirHgq0d6L4xe3fkSA2Wrj6dqQYeN8j9CH8kVfDY7VPaDlvKqfiPA7f jDt/O05a5HIw8HC87t9SAFh0b/+3fLPMSUX0DlEyr+U1bedjnqggU3197I53GD4Z Puurm+hOQz9JUB0ssvV56atTl8vR077ap6m5nFNrvKwkQtp1baGIff1MCCyld7Wm 9B8RTj0DL/IsdQKCAQEA4YL+NEElhZEi8c5oGYjFD0BVv1zy+RY46xbmDJAfgeSF otuolAbzCZTTmBZAFwhgbm5mRDBi6FKlerMiMu0ZnZZGOu88TtPhMFO0V9hWCSrk zJ7GSOQOsCyvMtTnlvfUiEJLlz7Ez2BI8bdcPS2R+gDEU+L/xndSlZerxS9bYZ36 hjCjQQY1NYV+4SuAWdHF3K1/sqSYmqP+4ItkzQgqYx8MVeD+hBy4ydt9/z2N4l5q CCaCKHl3NHSyc5NaVc26fSVIDJon5oMpIKuvFXSZLp99wN6lyuM79EHPZ4uKJ+Ws lxbSbCSf3a6YazxJ3Wa8fqQCCnZz9qUNbS5lnxI30QKCAQEAnvrsgjtmnnj56M3K 3DIJ4bzWnFhM8cp8qN9MbKOEwy5SHLNsX/LnKut4PwFKVGQbRHQR2WayHS3FQgPD VjRGRAsI5XReRKrbGHxBPB7wUv3t9L9fx/6/uXPI0Tip08NFfzgYGVGqkPVP7q3q zvBPXzwOfhMh5LqNlQlrDzDZyFcQp89LxhDL75K4xvx/ab2VWCsaN3A1phAm/YLu JnIqLrOdv6fvJIDR3aJRNd2EHB+pg5MTcx1DjM8QPtrlnvMwy4z/upVN89M6L6PY ecmK3x4H0QCKthYHwpzODmYQPxvU0N3T6MAgjJkehUS9C/sK8Xeia3XeSRpOVuUR Jg1aiQKCAQBpVrwRgqDZTFtrYkQkMUwDfMTRF8Tzb1r1DYQ2OBhPn4SA92Q+iSR7 xEY2k6YqXCJXU0B397Yr2Xcs4oJGEZTWlLyHiPq9w6E75s2l/wrpS10G69+pasYl qynHb8YiMe/YGkefBZX/XHWWW1U9yoyWoEcU/EMH3PL5p5h4ClAAQEAm8I9Br3mp X4tN+PkCe8eV9oFg3BvgmFxVXSef0oGiPX2QnAGnipcSy3GGiaU+KWYUGpDQATUa jqxQ6nLXA7tp+8E1Cck7lubeOPQW7JklOyENt7LwG8cEqL0OHBNT4agGB4YRN+Zp McZGySmjvvqrmUbOxlgSzTSBXJvheywRAoIBAQCEYF36emo3siGpptI1v0ygFvM5 MCMxMWZWqZw1yweKmbCuA/HkdgLGfPlxFUnBEmeNCWcsTg/otkmuNkI+4vv3nD0G SrUCl1zg+OF/Aj7VD4lnCrKGPqkQJIIRUDAfADDmi5w9XCAK0HJhOqtaiPUPLZXq kVIcRw/XoXzdcaY05GUapAkZug4iaEoSdgcTRhAzHQn+Tt/T8R4LVtjXii6GwVv1 UpitS9WJsg2RWlheQZoOZLtFHB0DRzHKaHNP+0nVVRbjhB3dpUs9R/ZELzCRKoUo LUW5+5yfgFZjRruQEolr/QBlarMzQ2cOsHYNHWXL9SNLynwlE4AF59iJ7TJS -----END RSA PRIVATE KEY-----';

  SecretKey? _secretKey;
  // AES-GCM algorithm (256-bit)
  final AesGcm aesGcm = AesGcm.with256bits();

  /// Initializes the AES-GCM key. If no key is set, a new random 256-bit key is generated.
  Future<void> _initAead() async {
    if (_secretKey == null) {
      final random = Random.secure();
      final keyBytes = List<int>.generate(32, (_) => random.nextInt(256));
      _secretKey = SecretKey(Uint8List.fromList(keyBytes));
    }
  }

  /// Resets the current key and reinitializes a new one.
  Future<void> resetKeyUsed() async {
    _secretKey = null;
    await _initAead();
  }

  /// Returns the “public key” representation of the keyset by encrypting it with the provided RSA public key.
  Future<String> getKeyUsed() async {
    try {
      final keysetBytes = await _getKeyset();
      return await parsePublicKey(keysetBytes, _pk);
    } catch (e) {
      print("Error in getKeyUsed: $e");
    }
    return "";
  }

  /// Encrypts [plainText] using RSA with OAEP padding. In this implementation, the provided
  /// base64 encoded public key is decoded (after stripping PEM headers) and used to encrypt the data.
  ///
  /// Note: The RSA part uses [pointycastle] and assumes the public key is in a format similar to PEM.
  Future<String> parsePublicKey(Uint8List plainText, String base64PublicKey) async {
    try {
      // Decode the provided public key.
      final decoded = base64Decode(base64PublicKey);
      String pubKeyPEM = utf8.decode(decoded);
      pubKeyPEM = pubKeyPEM
          .replaceAll("-----BEGIN PUBLIC KEY-----\n", "")
          .replaceAll("-----END PUBLIC KEY-----", "")
          .trim();
      // For PointyCastle, we re-add the PEM header/footer.
      final fullPem =
          "-----BEGIN PUBLIC KEY-----\n$pubKeyPEM\n-----END PUBLIC KEY-----";
      // Parse the RSA public key.
      final rsaPublicKey = RSAKeyParser()
          .parse(fullPem) as RSAPublicKey;

      // Initialize OAEP cipher with SHA-256.
      final oaep = OAEPEncoding(RSAEngine());
      oaep.init(
          true, PublicKeyParameter<RSAPublicKey>(rsaPublicKey));

      // Encrypt the plaintext.
      final encrypted = oaep.process(plainText);
      return base64Encode(encrypted);
    } catch (e) {
      print("Error in parsePublicKey: $e");
    }
    return "";
  }

  /// Retrieves the keyset bytes (in this case, the raw AES key).
  Future<Uint8List> _getKeyset() async {
    await _initAead();
    final keyBytes = await _secretKey!.extractBytes();
    return Uint8List.fromList(keyBytes);
  }

  /// Saves the key (as a base64 string) to SharedPreferences.
  Future<void> saveKeyToCache() async {
    // if (_secretKey != null) {
    //   final keyBytes = await _secretKey!.extractBytes();
    //   final keySaved = base64Encode(keyBytes);
    //   print("Saving key: $keySaved");
    //   await LocalStorage().getData(_keysetName, keySaved);
    //   await resetKeySavedInSharedPref();
    // }
  }

  /// Loads a saved key from SharedPreferences and sets it as the current key.
  Future<bool> loadKeySavedToKeySet() async {
    // final keySaved = LocalStorage().getData(_keysetName) ?? "";
    // print("Loaded key from prefs: $keySaved");
    // if (keySaved.isNotEmpty) {
    //   await saveKeyToSharedPref(keySaved);
    //   final keyBytes = base64Decode(keySaved);
    //   _secretKey = SecretKey(Uint8List.fromList(keyBytes));
    //   await _initAead();
    //   await resetKeySavedInSharedPref();
    //   return true;
    // }
    return false;
  }

  Future<void> resetKeySavedInSharedPref() async {
    // await LocalStorage().saveData(_keysetName, '');
  }

  Future<void> saveKeyToSharedPref(String keySaved) async {
    // await LocalStorage().saveData(_keysetName, keySaved);
  }

  /// Clears the key cache.
  Future<void> clearCacheTinkKey() async {
    await resetKeySavedInSharedPref();
    // await LocalStorage().saveData(_keysetName, "");
  }

  /// Encrypts a [String] by first converting it to UTF-8 bytes then encrypting it with AES‑GCM.
  /// Returns the result as a base64 encoded string.
  Future<String> encryptData(String data) async {
    return encryptDataBytes(utf8.encode(data));
  }

  /// Encrypts raw byte [data] with AES‑GCM.
  /// A random 12-byte nonce is generated and prepended to the result.
  Future<String> encryptDataBytes(List<int> data) async {
    try {
      // Generate a random 12-byte nonce.
      final random = Random.secure();
      final nonce = List<int>.generate(12, (_) => random.nextInt(256));

      final secretBox = await aesGcm.encrypt(
        data,
        secretKey: _secretKey!,
        nonce: nonce,
      );
      // Combine nonce, ciphertext, and MAC.
      final combined = nonce + secretBox.cipherText + secretBox.mac.bytes;
      return base64Encode(combined);
    } catch (e) {
      print("Error in encryptDataBytes: $e");
    }
    return "";
  }

  /// Decrypts a base64 encoded string that was encrypted with [encryptData].
  Future<String> decryptData(String data) async {
    final decoded = base64Decode(data);
    try {
      // The nonce is 12 bytes; the MAC is 16 bytes (AES-GCM).
      final nonce = decoded.sublist(0, 12);
      final macBytes = decoded.sublist(decoded.length - 16);
      final cipherText = decoded.sublist(12, decoded.length - 16);

      final secretBox = SecretBox(
        cipherText,
        nonce: nonce,
        mac: Mac(macBytes),
      );
      final clearText = await aesGcm.decrypt(
        secretBox,
        secretKey: _secretKey!,
      );
      return utf8.decode(clearText);
    } catch (e) {
      print("Error in decryptData: $e");
    }
    return "";
  }

  /// Decrypts data contained in a JSON string (expected to be in the same format as produced by [buildStringSend]).
  Future<String> decryptRawData(String rawData) async {
    try {
      final Map<String, dynamic> jsonMap = jsonDecode(rawData);
      final dataObj = DataBaseObj.fromJson(jsonMap);
      return await decryptData(dataObj.data);
    } catch (e) {
      print("Error in decryptRawData: $e");
    }
    return "";
  }

  /// Builds a JSON string that wraps the AES‑GCM encrypted data.
  Future<String> buildStringSend(String plainText) async {
    try {
      print("buildStringSend plainText: $plainText");
      final cipherText = await encryptData(plainText);
      final dataObj = DataBaseObj(data: cipherText);
      final jsonData = jsonEncode(dataObj.toJson());
      print("buildStringSend DataSend: $jsonData");
      return jsonData;
    } catch (e) {
      print("Error in buildStringSend: $e");
    }
    return "";
  }

  Future<String> rsaEncryptWithPublicKey(String plainText) async {
    try {
      // final publicKeyPEM = formatPemKey(_pk, header: publicHeader, footer: publicFooter);
      final publicKeyPEM = _pk;

      // Chuẩn hoá chuỗi PEM nếu cần (đảm bảo có header/footer)
      String formattedPublicKey = publicKeyPEM;
      if (!publicKeyPEM.contains("-----BEGIN PUBLIC KEY-----")) {
        formattedPublicKey =
        "-----BEGIN PUBLIC KEY-----\n$publicKeyPEM\n-----END PUBLIC KEY-----";
      }

      // Sử dụng RSAKeyParser (từ package:encrypt) để phân tích public key
      final rsaPublicKey = RSAKeyParser().parse(formattedPublicKey) as RSAPublicKey;

      // Khởi tạo OAEP cipher với SHA-256
      final oaep = OAEPEncoding(RSAEngine());
      oaep.init(
        true, // true cho mã hóa
        PublicKeyParameter<RSAPublicKey>(rsaPublicKey),
      );

      // Chuyển plainText thành bytes
      final plainBytes = Uint8List.fromList(utf8.encode(plainText));

      // Mã hóa dữ liệu
      final encryptedBytes = oaep.process(plainBytes);

      // Trả về kết quả mã hóa dưới dạng base64
      return base64Encode(encryptedBytes);
    } catch (e) {
      print("Error in rsaEncryptWithPublicKey: $e");
      return "";
    }
  }

  /// Giải mã chuỗi [encryptedBase64] sử dụng RSA với private key.
  /// [privateKeyPEM] là chuỗi PEM chứa private key.
  Future<String> rsaDecryptWithPrivateKey(String encryptedBase64) async {
    // final privateKeyPEM = formatPemKey(_pvk, header: privateHeader, footer: privateFooter);
    final privateKeyPEM = _pvk;

    try {
      // Chuẩn hoá chuỗi PEM nếu cần (đảm bảo có header/footer)
      String formattedPrivateKey = privateKeyPEM;
      if (!privateKeyPEM.contains("-----BEGIN PRIVATE KEY-----")) {
        formattedPrivateKey =
        "-----BEGIN PRIVATE KEY-----\n$privateKeyPEM\n-----END PRIVATE KEY-----";
      }

      // Sử dụng RSAKeyParser để phân tích private key
      final rsaPrivateKey = RSAKeyParser().parse(formattedPrivateKey) as RSAPrivateKey;

      // Khởi tạo OAEP cipher với SHA-256
      final oaep = OAEPEncoding(RSAEngine());
      oaep.init(
        false, // false cho giải mã
        PrivateKeyParameter<RSAPrivateKey>(rsaPrivateKey),
      );

      // Giải mã từ base64 về byte
      final encryptedBytes = base64Decode(encryptedBase64);

      // Giải mã dữ liệu
      final decryptedBytes = oaep.process(encryptedBytes);

      // Chuyển đổi kết quả thành chuỗi UTF-8
      return utf8.decode(decryptedBytes);
    } catch (e) {
      print("Error in rsaDecryptWithPrivateKey: $e");
      return "";
    }
  }

  String formatPemKey(String key, {required String header, required String footer}) {
    // Loại bỏ các khoảng trắng thừa.
    key = key.replaceAll(RegExp(r'\s+'), '');

    // Tách chuỗi key body từ header và footer nếu có.
    if (key.startsWith(header)) {
      key = key.substring(header.length);
    }
    if (key.endsWith(footer)) {
      key = key.substring(0, key.length - footer.length);
    }

    // Chia key thành các dòng 64 ký tự.
    final regex = RegExp(r'.{1,64}');
    final matches = regex.allMatches(key).map((m) => m.group(0)).join('\n');

    return '$header\n$matches\n$footer';
  }

  // Định nghĩa header và footer cho public và private key:
  String publicHeader = "-----BEGIN PUBLIC KEY-----";
  String publicFooter = "-----END PUBLIC KEY-----";
  String privateHeader = "-----BEGIN RSA PRIVATE KEY-----";
  String privateFooter = "-----END RSA PRIVATE KEY-----";

}

main() {
  String data = 'ufothuan';
  print('data: $data');
  encryptData(data);
}

void encryptData(String data) async {

  await CryptoInterface().encryptData(data).then((value) async {
    print('edata: $value');

    await CryptoInterface().decryptData(value).then((valuedecrypt) {
      print('clear data: $valuedecrypt');
    });
  });
}