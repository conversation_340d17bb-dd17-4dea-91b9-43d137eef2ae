import 'dart:convert';
import 'dart:convert';
import 'dart:convert';

import 'package:encrypt/encrypt.dart';
import 'package:encrypt/encrypt_io.dart';
import 'dart:convert';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart';
import 'package:pointycastle/pointycastle.dart';

import 'local_storage.dart';

class RSAEncryption {
  static const String _publicKeyPem = '''-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAzpiDrm/MtLhuFZLjzjJp
E8q+aOCSPZsd9U7q9al/5UTyOsS+AHlet0T/SXVpRh8ACKZbG6zSLHTGtU6LQvEi
4j/CnCy7+cLouChDSSzLfSfidsEEjuAMNIiUaRDKrhi9Uze+ZDnp6LeipGLZ+gDV
alksLp9ashesMwuPjl/pD7aALj70kmAgYqPjso7V7WotThpnKa1b5BNFkZy4VLFb
i+JSn581+xHk0mD8SbVKR/plYMh4Lxi/FfotQxIGK8N4G4sbC+FfC1pUF7qUyzv3
Jp6bRyPKPo00eWXAFE4enk2FOjbSTrrMkUUkxHPQvn6//0+1dGTKdiiw2H8Py1Jm
e0AZbRGugWDCi0DxLtW/c2nU4tAqdl0BIgmwN1t4no9VCxlkG9tyhCiFAU+TdusM
3W8WD8/paLpsvmIkJqSa7QlvN+176qNgun3+SGplim/7oGL8TqSvagjsBSBhn8Ua
7BlT4wZM25dLrH567cJaTX0bBzkMDRcyqfQnpFJHOYl3HKjkLjF3smApjrluExFv
AnD+6BVpcujO9WtXAycxAX3rwtCUP+GLBS9izRVWwu43dJBlelijEa4Izn9+VnKH
/DMVGKKC76Sh25+tdrg/lvNjQunoYF0Oj8bPdajcfVcfFUYTzNAwOHZqstuaMhqq
dXtttuAsrDohglXXs/d9boUCAwEAAQ==
-----END PUBLIC KEY-----''';


  static const String privateKey1 = "-----BEGIN RSA PRIVATE KEY-----\n" +
      "MIIJKAIBAAKCAgEAi6BkuSqJIHWITDwUYUvwAPypnVZyuRiVlzU9kPRXwweIrLH/\n" +
      "w0or3zO7vZkgygGoJkXf0eNUIfu1Z1Kiq9vhp6LTnBszEMYrDM18XZvkQnvlATcW\n" +
      "NnHOjHGfBx9vzkrAMWJyyKUeOSUmCnpyTToKOtoJdNUzNKMeOgOKC/38rirswnZW\n" +
      "+FnKGhR2BAurGKWVQ8urPt53fMz1qztqvkvBZJ4vXpnEI3kq8ESEYAPjn0lW02jA\n" +
      "CJ5xFXgxYamdLkHa8pAH/bPD4dQrwN78EmODFveEtdZYhsYLU4doh1rozn1y7rfD\n" +
      "X0ocUzRy0UCbNu+7IlROs1/EsJDbmmufEs6i58Miw5oDrBi+o74nljf87KWEQhb5\n" +
      "MUfloZyPCUPpNXJqi4PmCzFcel5LyTraowMassh3Gvl4O0hP9aAUA19vXyKnZPKM\n" +
      "0VYmQBWHixb4XZH23lo5Z/Mdvvg/sXHUEk1iFvMc9mXVur7Gua1puq51US8VskkR\n" +
      "yriniERf1eawta1rnmWXI17ZX037D1OsR9vXa6Yt0+3VL8pWZr5YRhRvy0Pynaek\n" +
      "ziOQnUQCG6fki4FsNmQokqx0GI1T4KyZ+3uI9by1daspn0WjKDtFEt4J70BcH9kp\n" +
      "bmr4IV8oJopM/tYe88TwrcO8MTTYTqlHNo47RZNWKZI/XgGjX1UNV+IjIh0CAwEA\n" +
      "AQKCAgAOEDy07dTcARqWWyLOXgCxiMFoyJFLlW9TyJgH5qSET1jz1AXn3KuokqRh\n" +
      "R3AnCJ2wJS+TC2uo9Jpj6s3Zs475/IuF2LN/CAbEBlp1b0NBcwsw4SSKnVHpkXQs\n" +
      "Gn1/cMYE816OWRs/ND+tpIGox6TKIrX7H5JFxfxAaaTY6l8uB8LLH3JJF3okwFTW\n" +
      "uXolRGBRcM0xyuSMHHTOJ3Dr8+NpyAkLZT99NzIc+zm+FgYyQAfPyWg2Ngc721xV\n" +
      "8uiTBS0Ra3Jymk2rjFxP0+Xd0Eoe4RVr30gRYxc29ZeQspZqXfZalwXFrkhGFS1P\n" +
      "aEt58BGQNp+qr4W3JxAHcLmYAlokCYzF64kGHBLKEzYTBbOTUhlxq8H931l/Y2+f\n" +
      "rVuSh8PdGBWQEA4/yPUsG9autqkSZ5Esv1V901kXQ2vJw6nUIoZpNm2u6FtyPCI4\n" +
      "a7r4C2f1q/nRbg3CFL7KGlcRY6LoI/5bpJvnzht+aoTvNiRXeCWW4wyyIOEJkzD9\n" +
      "MlLsAUQ5D76bZ966PV4zPWvgrqof1DDXhPoEHEr26DrafAkDok7G9nw+7SWSCCZn\n" +
      "mnoRWnRGH31+dVCnLQVHFjrfjN5lcprIS6JL/IquJyJvYqa4P48qcGrbxjH1QFtX\n" +
      "bl8heyzhlQ5F/gm/M6e8sPDhE0CTM5Wgk5F5h/4XZGjM4XTroQKCAQEA0/dPi9Wy\n" +
      "BcsEPYFr5oIYWjuPr2yu3F24LDalf/vHHupcaQb1ibf5pln7g4DGvew0eSuLrCf7\n" +
      "CrKLtO5wwrN7F4MX9/WBHo/Pynz1YBtwn0VoMDq42zHhfwKBq5XKqH3XUiX06p9j\n" +
      "9DNj6ROsTiHQPC5CLZ7zt56FGoTi0iEiC2bd8s1QyAkVmnHAAWw/88cRBfF4hSa8\n" +
      "69kwVOZit2sLA5nKGcDBfVJ+Wij8yfnMsqcs56aNqfNK+aS+nREoE8JrHBl8XaCW\n" +
      "iwUe67IEdmu7jyfw7hx3d4Ab/HOxqQKsYFCCqZoJImJqs0PFm/jnyOtSrjie9iRR\n" +
      "ROjs3F9y+gm6SQKCAQEAqKHz8kwpu7weyPjDDO3HPLYIQpBc+2/CxpiRzOBdowb+\n" +
      "5qU3VRo2XYucRMwNUPDdxClhcSOHc16346jrj4ZrEZjjuxH3wPqDFHUY9Fsrczdz\n" +
      "S/Lq0pxRaYmN5K43GH1AkDsBNgrtCEQfMz01E+d43P1tDrSbc41Rpj7990adb/sf\n" +
      "AAuXT8xxuHvAWMLMvi/Ta21tcxfQ50oq7MUgGax1CmKgs2x38aeF99TdNqm26ioK\n" +
      "sp+mqRs2xC7Mmq+T2geHIxAhO3CdvFYvZuX9pcgNNarHWPxvo3jRcbq1bKaq8ucx\n" +
      "s16Xbqw3dmnSrDZg6mbGGHgrMs9/oVL2O7TdGfsJNQKCAQBUwTXIZyko0dWfNknL\n" +
      "wMc2BUzdyypcKQlEfJFOjIFBJlrPEB5L9AlZ7+721R4iwPj2XmgZ7V6fF2dvcEwc\n" +
      "PiIA88U4u6zmaICgqbh/tlkhBmKzrjdORjKxU3JABsQi/TaEvXKiAhu4gHf5sYkb\n" +
      "MMAP4DtL/JvdFwCH6wy7Es6HGpmyxVAAvEksGUG6D+u+cSGoa7ZSH3w5Hf4KZJii\n" +
      "WKXtyaebBo0XrE2jhXIW3NBxuxDki59njkWzbqFdMogA5qNnd/VXhqfLbL/BZMwO\n" +
      "AePcSQ84Vp+aY9pM5oHqX4xHcsncQSBX5Fxf08CHLk6X8yh7S16cc0cfs7dKIsxZ\n" +
      "mFwpAoIBABnS0zyjlTB+mmhET/pG31/YsGMhIFwQvU8URh+NrEku5RIE8vhBT1n7\n" +
      "6y5N4Swn9o33mEhvHGr6ckaWs5wDBZpoQ1d+ZOZUdoZHeE7TN1ApiapzxKoD7iOv\n" +
      "QN9oHl/Nrnlnz8Oq4KmHZQ+/q8UkiUmuNWkvFy8W0Q4YT2PymGFxn0mufLlnoXKS\n" +
      "4N221wu43zNwSnZ5izAgF3qypTvJQRMaD2nqjmOfN9C/9DBOxQgO8Bxyfl8fH7/j\n" +
      "OsnlYUreJD+mVLB5im4w9IlZZwJgJEW3pVdnMOFjuw8jfGzrCPqxlNbXAXGdRdEI\n" +
      "W46rtSOwIV3V6Seaxvy5a6OxCrrTypECggEBAKjHQidLPZ5J654DXgC6xOZB/7ki\n" +
      "+JzbL4d4I20zWp1spwcC1m+GDl0qsD3I1nlRnTYiC0r34c3kpOxX2uglWF6X6H/H\n" +
      "3Fey0WPJ169bdk5Eil5EtnExaHC6IWe+LLJ8aGzD4uHTOoOkSnvWx2rwvFi/dziu\n" +
      "Igs/DfCHLRfxlOFUsjnrGKv9639kxrnFNVGVBezjkes73YPgak6uQGmXH1fd+WhJ\n" +
      "XeU6hl32jRmyZKpEVPBOuMAzuohHkRQN9HTTb5eW4In09AdT5jwgiRWszIcTGb9E\n" +
      "zHMCOS4SgRzjc+2J/+GkTiYu5r+0yrUwPq3i5eCrfcpGiKm3uMdNf/VLShY=\n" +
      "-----END RSA PRIVATE KEY-----";

  static const String publicKey1 = "-----BEGIN PUBLIC KEY-----\n" +
      "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAi6BkuSqJIHWITDwUYUvw\n" +
      "APypnVZyuRiVlzU9kPRXwweIrLH/w0or3zO7vZkgygGoJkXf0eNUIfu1Z1Kiq9vh\n" +
      "p6LTnBszEMYrDM18XZvkQnvlATcWNnHOjHGfBx9vzkrAMWJyyKUeOSUmCnpyTToK\n" +
      "OtoJdNUzNKMeOgOKC/38rirswnZW+FnKGhR2BAurGKWVQ8urPt53fMz1qztqvkvB\n" +
      "ZJ4vXpnEI3kq8ESEYAPjn0lW02jACJ5xFXgxYamdLkHa8pAH/bPD4dQrwN78EmOD\n" +
      "FveEtdZYhsYLU4doh1rozn1y7rfDX0ocUzRy0UCbNu+7IlROs1/EsJDbmmufEs6i\n" +
      "58Miw5oDrBi+o74nljf87KWEQhb5MUfloZyPCUPpNXJqi4PmCzFcel5LyTraowMa\n" +
      "ssh3Gvl4O0hP9aAUA19vXyKnZPKM0VYmQBWHixb4XZH23lo5Z/Mdvvg/sXHUEk1i\n" +
      "FvMc9mXVur7Gua1puq51US8VskkRyriniERf1eawta1rnmWXI17ZX037D1OsR9vX\n" +
      "a6Yt0+3VL8pWZr5YRhRvy0PynaekziOQnUQCG6fki4FsNmQokqx0GI1T4KyZ+3uI\n" +
      "9by1daspn0WjKDtFEt4J70BcH9kpbmr4IV8oJopM/tYe88TwrcO8MTTYTqlHNo47\n" +
      "RZNWKZI/XgGjX1UNV+IjIh0CAwEAAQ==\n" +
      "-----END PUBLIC KEY-----";


  static String encryptData(String data) {
    final publicKey = RSAKeyParser().parse(publicKey1) as RSAPublicKey;
    final encrypter = Encrypter(RSA(publicKey: publicKey));

    final encrypted = encrypter.encrypt(data);
    return base64Encode(encrypted.bytes);  // Mã hóa thành base64 để gửi đi
  }

  static String decryptData(String encryptedBase64) {
    final privateKey = RSAKeyParser().parse(privateKey1) as RSAPrivateKey;
    final encrypter = Encrypter(RSA(privateKey: privateKey));

    final decrypted = encrypter.decrypt(Encrypted(base64Decode(encryptedBase64)));
    return decrypted;
  }

  static Future<String> doAESDecrypt(String encryptedData) async {
    String AESKey = await LocalStorage().getData(LocalStorage.KEY_UFO, '');

    final key = Key.fromUtf8(AESKey.padRight(16, ' ')); // Đảm bảo key đủ 16 bytes
    final encryptedBytes = base64Decode(encryptedData);

    // 1. Tách IV và dữ liệu đã mã hóa
    final iv = IV(Uint8List.fromList(encryptedBytes.sublist(0, 16)));
    final encryptedText = Uint8List.fromList(encryptedBytes.sublist(16));

    // 2. Giải mã bằng AES/CBC/PKCS5Padding
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc, padding: 'PKCS7'));
    final decryptedText = encrypter.decrypt(Encrypted(encryptedText), iv: iv);

    return decryptedText;
  }

  static const String paddingKey = "0000000000000000";

  static Future<String> encryptAES_ECB(String data) async {
    String keyValue = await LocalStorage().getData(LocalStorage.KEY_UFO, '');
    if (keyValue == null){
      return '';
    }
    String encryptedData = '';
    var keyTemp = keyValue+paddingKey;
    keyTemp = keyTemp.substring(0,16);
    var keyEncrypt = Key.fromUtf8(keyTemp);
    final iv = IV.fromUtf8(paddingKey);
    try {
      print('->call encryptAES');
      final encrypter = Encrypter(AES(keyEncrypt, mode: AESMode.ecb));
      final encrypted = encrypter.encrypt(data, iv: iv);
      encryptedData = encrypted.base64;
      print("Encrypted data: "+ encryptedData);
    } catch (exception){
      print("Encrypted exception: "+ exception.toString());
    }
    return encryptedData;
  }

  static Future<String> doAESEncrypt(String plainText) async {
    String AESKey = await LocalStorage().getData(LocalStorage.KEY_UFO, '');
    final key = Key.fromUtf8(AESKey.padRight(16, ' ')); // Đảm bảo key đủ 16 bytes

    // 1. Tạo IV ngẫu nhiên 16 bytes
    final iv = IV.fromLength(16); // Hoặc dùng IV(Uint8List.fromList(...)) nếu cần cố định

    // 2. Mã hóa
    final encrypter = Encrypter(AES(key, mode: AESMode.cbc, padding: 'PKCS7'));
    final encrypted = encrypter.encrypt(plainText, iv: iv);

    // 3. Gộp IV và dữ liệu đã mã hóa
    final combined = iv.bytes + encrypted.bytes;
    // 4. Trả về dạng base64
    return base64Encode(combined);
  }

}

void main() {
  String message = "Hello, this is a secret message!";
  String encryptedMessage = RSAEncryption.encryptData(message);
  print("Encrypted: $encryptedMessage");
  String decryptedMessage = RSAEncryption.decryptData(encryptedMessage);
  print("Decrypted: $decryptedMessage");

}
