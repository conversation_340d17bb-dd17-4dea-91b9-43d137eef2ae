class ApiConstant {
  static const int connectTimeout = 30000;
  static const int receiveTimeout = 30000;

  // Url
  static const urlApi = '/api';
  static const urlSignUp = '/api/auth/v1/register';
  static const urlSignIn = '/api/auth/v1/login';
  static const urlAddCard = '/api/users/v1/card/add-card';
  static const urlRemoveCard = '/api/users/v1/card/remove-card';
  static const urlBanner = '/api/promo/v1/banner/get_banner';
  static const urlAllConfig = '/api/promo/v1/config/get_all_config';
  static const urlAllBank = '/api/promo/v1/bank/get_all_bank';
  // static const urlGetMerchantNearbyPromo = '/api/promo/merchants/nearby';
  static const urlGetAllBankCard = '/api/promo/v1/bank/all_bank_card';
  static const urlOpenCard = '/api/promo/v1/bank/request_open_card';
  static const urlLogin = '/api/users/v1/login';
  static const urlFindUser = '/api/users/v1/find';
  static const urlFindUserByName = '/api/users/v1/find_name';
  static const urlUfo = '/api/promo/v1/security/get_ufo';

  // Đang dùng
  static const urlGetPromo = '/api/promo/v1/get_promo';
  static const urlGetDetailPromo = '/api/promo/v1/get-detail';
  static const urlGetMerchantNearbyPromo = '/api/promo/v1/nearby-promo';
  static const urlContributionCreatePromo = '/api/promo/v1/contributions/create';

  //portal
  static const urlGetAllMerchant = '/api/promo/merchants/get_all_merchant';
  static const urlDeleteMc = '/api/promo/merchants/delete-merchant';
  static const urlAddMerchant = '/api/promo/merchants/add_merchant';
  static const urlUpdateMerchant = '/api/promo/merchants/update-merchant';
  // promo
  static const urlGetMerchantCreatePromo = '/api/promo/v1/create-new-promo';


  static const SUPPORT_EMAIL = '<EMAIL>';
  static const SUPPORT_PHONE = '0332193377';

  static const String logger_Storage = 'LOG_STORAGE';
  static const String LOGGER_TYPE_ACTION = 'ACTION';
  static const String LOGGER_TYPE_REQUEST = 'REQUEST';
  static const String LOGGER_TYPE_RESPONSE = 'RESPONSE';

  static int DO_SERVICE_SUCCESS = 1000;

  // User APIs
  static const String urlSearchUser = '/api/user/search';
  static const String urlBlockUser = '/api/user/block';

  // Card Request APIs
  static const String urlApproveCardRequest = '/api/card-request/approve';
  static const String urlRejectCardRequest = '/api/card-request/reject';

  // Other APIs
  static const String urlGetAllPromo = '/api/promo/get-all';
  static const String urlAddPromo = '/api/promo/add';
  static const String urlUpdatePromo = '/api/promo/update';
  static const String urlDeletePromo = '/api/promo/delete';
  static const String urlGetAllBank = '/api/bank/get-all';
  static const String urlAddBank = '/api/bank/add';
  static const String urlUpdateBank = '/api/bank/update';
  static const String urlDeleteBank = '/api/bank/delete';
}

enum PromoCategory {
  MUA_SAM("Mua sắm"),
  AN_UONG("Ăn uống"),
  DU_LICH("Du lịch"),
  GIAI_TRI("Giải trí"),
  SUC_KHOE("Sức khỏe"),
  THOI_TRANG("Thời trang"),
  GIAO_DUC("Giáo dục"),
  KHAC("Khác");

  final String displayName;
  const PromoCategory(this.displayName);
}

// enum BankCode {
//   NONE("", "Gợi ý"),
//   TCB("TCB", "Techcombank"),
//   VPB("VPB", "VPBank"),
//   ACB("ACB", "ACB Bank"),
//   SCB("SCB", "Saigon Commercial Bank"),
//   BID("BID", "BIDV"),
//   HDB("HDB", "HDBank");
//
//   final String code;
//   final String name;
//
//   const BankCode(this.code, this.name);
//
// }