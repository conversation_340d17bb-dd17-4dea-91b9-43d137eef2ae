class AppDimens {
  // Space
  static const spaceXSmall4 = 4.0;
  static const spaceXSmall6 = 6.0;
  static const spaceXSmall8 = 8.0;
  static const spaceXSmall10 = 10.0;
  static const spaceXSmall = 12.0;
  static const spaceSmall = 14.0;
  static const spaceMedium = 16.0;
  static const spaceLarge = 18.0;
  static const spaceLarge24 = 24.0;
  static const spaceLarge32 = 32.0;
  static const spaceLarge48 = 48.0;
  static const spaceLarge64 = 64.0;

  // Text size
  static const textSize10 = 10.0;
  static const textSizeXSmall = 12.0;
  static const textSizeSmall = 14.0;
  static const textSizeMedium = 16.0;
  static const textSizeLarge = 18.0;
  static const textSizeLarge20 = 20.0;
  static const textSizeLarge24 = 24.0;
  static const textSizeLarge26 = 26.0;
  static const textSizeLarge28 = 28.0;

  // Line height
  static const lineHeightXSmall = 16.0;
  static const lineHeightSmall = 20.0;
  static const lineHeightMedium = 24.0;
  static const lineHeightXLarge = 28.0;
  static const lineHeightLarge = 32.0;

  // Buttom size
  static const buttonSizeXSmall = 24.0;
  static const buttonSizeCompact = 32.0;
  static const buttonSizeSmall = 40.0;
  static const buttonSizeNormal = 48.0;

  // Stepper size

  static const stepperSizeSmall = 24.0;
  static const stepperSizeMedium = 32.0;
  static const stepperSizeLarge = 40.0;

  static const radiusXSmall = 6.0;
  static const radiusSmall = 8.0;
  static const radiusMedium = 10.0;
  static const radiusLarge = 15.0;

  // Height Components
  static const heightTextField = 52.0;
  static const heightTextView = 86.0;
  static const heightIconSize = 24.0;
  static const heightLogoItem = 54.0;
  static const heightButton = 50.0;

  // static const heightCellSmall = 44.0;
  // static const heightCellMedium = 48.0;
  // static const heightCellLarge = 96.0;

  static const noteMaxLength = 225;

  // Icon size
  static const iconXSmall = 14.0;
  static const iconSmall = 16.0;
  static const iconMedium = 18.0;
  static const icon20 = 20.0;
  static const icon24 = 24.0;
  static const icon28 = 28.0;
  static const icon40 = 40.0;
  static const icon46 = 46.0;
  static const icon60 = 60.0;
  static const icon80 = 80.0;

  // Bill size
  static const billWidth = 360.0;
}
