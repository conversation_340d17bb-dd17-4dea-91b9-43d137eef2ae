import 'dart:convert';
import 'dart:math';
import 'package:card_promo/util/api_constant.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../data/model/bank_data.dart';
import '../data/model/card_data.dart';
import '../data/model/promo_detail.dart';

class LocalStorage {
  static const KEY_LANGUAGE_APP = 'KEY_LANGUAGE_APP';

  static const storage = FlutterSecureStorage();

  static const KEY_LOGGER = 'KEY_LOGGER';
  static const KEY_PROMO_FAVOURITE = 'KEY_PROMO_FAVOURITE';
  static const KEY_BANK_CODE = 'KEY_BANK_CODE';
  static const KEY_BANNER = 'KEY_BANNER';
  static const KEY_BANK_CONFIG = 'KEY_BANK_CONFIG';
  static const KEY_MC_CATEGORY_CONFIG = 'KEY_MC_CATEGORY_CONFIG';
  static const KEY_UFO = 'KEY_UFO';
  static const KEY_OPEN_APP = 'KEY_OPEN_APP';
  static const KEY_INDEX_CARD = 'selectedIndexCard';
  static const KEY_CONTRIBUTION_POINT = 'KEY_CONTRIBUTION_POINT';
  static const KEY_RECENT_MERCHANTS = 'KEY_RECENT_MERCHANTS';

  Future clear(String key) async {
    await storage.delete(key: key);
  }

  Future clearAll() async {
    await storage.deleteAll();
  }

  setLanguageApp(String? value) {
    saveData(KEY_LANGUAGE_APP, value);
  }

  Future<String> getLanguageApp() async {
    return await getData(KEY_LANGUAGE_APP, '');
  }

  Future<void> saveData(String key, dynamic value) async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(key, value);
    } else {
      await storage.write(key: key, value: value.toString());
    }
    print('Saved data: ${value.toString()}');
  }

  dynamic getData(String key, [dynamic defaultData]) async {
    if (kIsWeb) {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(key) ?? '';
    }
    if (await storage.containsKey(key: key)) {
      String? value = await storage.read(key: key);
      if (defaultData == null) {
        return value;
      } else {
        try {
          if (defaultData is bool) {
            if (value!.toLowerCase() == 'true') {
              return true;
            } else if (value.toLowerCase() == 'false') {
              return false;
            } else {
              return defaultData;
            }
          } else if (defaultData is int) {
            return int.parse(value!);
          } else if (defaultData is double) {
            return double.parse(value!);
          } else {
            return value;
          }
        } catch (e) {
          print(e);
          return defaultData;
        }
      }
    }
    return defaultData;
  }

  Future<void> saveDataToLast(String key, dynamic value) async {
    String? currentData;
    if (await storage.containsKey(key: key)) {
      currentData = await storage.read(key: key);
    }
    storage.write(
        key: key,
        value:
            '${currentData != null ? ('$currentData\n') : ''}${value.toString()}');
  }

  Future<PromoDetail?> getDetailFavouritePromo(
      PromoDetail promoFavourite) async {
    print('get detail fvr');
    List<PromoDetail> listPromo = [];

    String promoSaved = await getData(KEY_PROMO_FAVOURITE, '');
    if (promoSaved.isNotEmpty) {
      listPromo = List<PromoDetail>.from(
          jsonDecode(promoSaved).map((x) => PromoDetail.fromJson(x)));
      for (PromoDetail data in listPromo) {
        if (data.id == promoFavourite.id) {
          return data;
        }
      }
    }
    return null;
  }

  Future<List<PromoDetail>?> getAllFavouritePromo() async {
    print('get all fvr');
    List<PromoDetail> listPromo = [];

    String promoSaved = await getData(KEY_PROMO_FAVOURITE, '');
    if (promoSaved.isNotEmpty) {
      listPromo = List<PromoDetail>.from(
          jsonDecode(promoSaved).map((x) => PromoDetail.fromJson(x)));
      return listPromo;
    }
    return null;
  }

  Future<void> saveFavouritePromo(PromoDetail promoFavourite) async {
    print('save fvr');
    List<PromoDetail> listPromo = [];

    String promoSaved = await getData(KEY_PROMO_FAVOURITE, '');
    if (promoSaved.isEmpty) {
      listPromo.add(promoFavourite);
    } else {
      listPromo = List<PromoDetail>.from(
          jsonDecode(promoSaved).map((x) => PromoDetail.fromJson(x)));
      bool isSaved = false;
      for (PromoDetail data in listPromo) {
        if (data.id == promoFavourite.id) {
          isSaved = true;
          break;
        }
      }
      if (!isSaved) {
        listPromo.add(promoFavourite);
      }
    }
    await saveData(KEY_PROMO_FAVOURITE, jsonEncode(listPromo));
  }

  Future<void> removeFavouritePromo(PromoDetail promoFavourite) async {
    print('remove fvr');
    List<PromoDetail> listPromo = [];

    String promoSaved = await getData(KEY_PROMO_FAVOURITE, '');
    if (promoSaved.isNotEmpty) {
      listPromo = List<PromoDetail>.from(jsonDecode(promoSaved).map((x) => PromoDetail.fromJson(x)));
      for (PromoDetail data in listPromo) {
        if (data.id == promoFavourite.id) {
          listPromo.remove(data);
          break;
        }
      }
    }
    await saveData(KEY_PROMO_FAVOURITE, jsonEncode(listPromo));
  }

  Future<List<CardData>> getAllBankCard() async {
    print('get all fvr');
    List<CardData> list = [];

    String bankSaved = await getData(KEY_BANK_CODE, '');
    if (bankSaved.isNotEmpty) {
      List<dynamic> jsonList = jsonDecode(bankSaved);
      list = jsonList.map((json) => CardData.fromJson(json)).toList();
    }
    return list;
  }

  Future<void> saveBankCard(CardData card) async {
    print('save fvr');
    List<CardData> list = [];

    String bankSaved = await getData(KEY_BANK_CODE, '');
    if (bankSaved.isEmpty) {
      list.add(card);
    } else {
      List<dynamic> jsonList = jsonDecode(bankSaved);
      list = jsonList.map((json) => CardData.fromJson(json)).toList();

      bool isSaved = false;
      for (var data in list) {
        if ((data.name == card.name) && (data.pan == card.pan) && (data.scheme == card.scheme)) {
          isSaved = true;
          break;
        }
      }
      if (!isSaved) {
        list.add(card);
      }
    }
    List<Map<dynamic, dynamic>> jsonList = list.map((card) => card.toJson()).toList();
    await saveData(KEY_BANK_CODE, jsonEncode(jsonList));
  }

  Future<void> removeBankCard(CardData card) async {
    List<CardData> list = [];

    String bankSaved = await getData(KEY_BANK_CODE, '');
    if (bankSaved.isNotEmpty) {
      List<dynamic> jsonList = jsonDecode(bankSaved);
      list = jsonList.map((json) => CardData.fromJson(json)).toList();
      for (var cardData in list) {
        if (cardData.name == card.name) {
          list.remove(cardData);
          break;
        }
      }
    }
    List<Map<dynamic, dynamic>> jsonList = list.map((card) => card.toJson()).toList();
    await saveData(KEY_BANK_CODE, jsonEncode(jsonList));
  }

  Future<List<Map<String, String>>> getRecentMerchants() async {
    String merchantsData = await getData(KEY_RECENT_MERCHANTS, '');
    if (merchantsData.isNotEmpty) {
      List<dynamic> jsonList = jsonDecode(merchantsData);
      return jsonList.map((item) => Map<String, String>.from(item)).toList();
    }
    return [];
  }

  Future<void> saveRecentMerchant(String merchantId, String merchantName, String? iconUrl) async {
    List<Map<String, String>> recentMerchants = await getRecentMerchants();

    // Remove if already exists to avoid duplicates
    recentMerchants.removeWhere((merchant) => merchant['id'] == merchantId);

    // Add to beginning of list
    recentMerchants.insert(0, {
      'id': merchantId,
      'name': merchantName,
      'icon': iconUrl ?? '',
    });

    // Keep only last 10 recent merchants
    if (recentMerchants.length > 10) {
      recentMerchants = recentMerchants.take(10).toList();
    }

    await saveData(KEY_RECENT_MERCHANTS, jsonEncode(recentMerchants));
  }

  Future<void> clearRecentMerchants() async {
    await clear(KEY_RECENT_MERCHANTS);
  }
}
