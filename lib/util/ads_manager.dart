import 'dart:io';
import 'dart:ui';

import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdsManager {
  static final AdsManager _instance = AdsManager._internal();

  factory AdsManager() => _instance;

  AdsManager._internal() {}

  InterstitialAd? _interstitialAd;
  int _numInterstitialLoadAttempts = 0;
  int maxFailedLoadAttempts = 3;
  BannerAd? bannerAd;
  static final AdRequest request = const AdRequest();
  AppOpenAd? _appOpenAd;

  Future<void> createBanner(Function callbackSuccess) async {
    BannerAd(
      // adUnitId: 'ca-app-pub-3940256099942544/2934735716', // test ios
      // adUnitId: 'ca-app-pub-3940256099942544/6300978111',
      // test android
      adUnitId: Platform.isAndroid ? 'ca-app-pub-4761500266920887/7778377079' : 'ca-app-pub-4761500266920887/5756044507', //prod
      request: AdRequest(),
      size: AdSize.banner,
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          print("onAdLoaded banner");
          bannerAd = ad as BannerAd;
          callbackSuccess();
        },
        onAdFailedToLoad: (ad, err) {
          print('Failed to load a banner ad: ${err.message}');
          ad.dispose();
        },
      ),
    ).load();
  }

  Future<void> createInterstitialAd() async {
    await MobileAds.instance.initialize();
    print("createInterstitialAd");
    InterstitialAd.load(
        //test
        adUnitId: Platform.isAndroid
            ? 'ca-app-pub-3940256099942544/1033173712'
            : 'ca-app-pub-3940256099942544/4411468910',

        // prod
        // adUnitId: Platform.isAndroid
        //     ? 'ca-app-pub-3363022520269703/9857388035'
        //     : 'ca-app-pub-3363022520269703/7445033024',
        request: request,
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd ad) {
            print('loaded InterstitialAd');
            _interstitialAd = ad;
            _numInterstitialLoadAttempts = 0;
            _interstitialAd!.setImmersiveMode(true);
          },
          onAdFailedToLoad: (LoadAdError error) {
            print('InterstitialAd failed to load: $error.');
            _numInterstitialLoadAttempts += 1;
            _interstitialAd = null;
            if (_numInterstitialLoadAttempts < maxFailedLoadAttempts) {
              createInterstitialAd();
            }
          },
        ));
  }

  void showInterstitialAd(VoidCallback callback) {
    if (_interstitialAd == null) {
      print('Warning: attempt to show interstitial before loaded.');
      callback.call();
      return;
    }
    _interstitialAd!.fullScreenContentCallback = FullScreenContentCallback(
      onAdShowedFullScreenContent: (InterstitialAd ad) => {
        print('ad onAdShowedFullScreenContent.'),
      },
      onAdDismissedFullScreenContent: (InterstitialAd ad) {
        print('$ad onAdDismissedFullScreenContent.');
        ad.dispose();
        createInterstitialAd();
        callback.call();
      },
      onAdFailedToShowFullScreenContent: (InterstitialAd ad, AdError error) {
        print('$ad onAdFailedToShowFullScreenContent: $error');
        ad.dispose();
        createInterstitialAd();
        callback.call();
      },
    );
    _interstitialAd!.show();
  }

  void createAdsOpenApp(Function callback) {
    AppOpenAd.load(
      // adUnitId: 'ca-app-pub-3940256099942544/9257395921', // dev
      adUnitId: Platform.isAndroid ? 'ca-app-pub-4761500266920887/4960642042' : 'ca-app-pub-4761500266920887/1199283011', //prod
      request: const AdRequest(),
      adLoadCallback: AppOpenAdLoadCallback(
        onAdLoaded: (ad) {
          _appOpenAd = ad;
          print('AppOpenAd loaded');
          callback();
        },
        onAdFailedToLoad: (error) {
          print('AppOpenAd failed to load: $error');
          _appOpenAd = null;
        },
      ),
      orientation: AppOpenAd.orientationPortrait,
    );
  }

  void showAdsOpenApp() {
    if (_appOpenAd!= null) {
      _appOpenAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          print('AppOpenAd dismissed');
          _appOpenAd = null;
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          print('AppOpenAd failed to show: $error');
          _appOpenAd = null;
          createAdsOpenApp(() {
            _appOpenAd!.show();
          });
        },
        onAdShowedFullScreenContent: (ad) {
          print('AppOpenAd showed');
        },
      );

      _appOpenAd!.show();
    }
  }
}
