// check

import 'package:flutter/widgets.dart';

import '../res/string/app_strings.dart';

convertPhoneToOrigin(String phone) {
  String phoneNumber = phone.trim();
  if (phoneNumber.length != 0) {
    phoneNumber = phoneNumber.replaceFirst('(+84)', '0');
    phoneNumber = phoneNumber.replaceFirst('+84', '0');
    // phoneNumber = phoneNumber.replace('0084', '0');
    phoneNumber = phoneNumber.replaceAll(' ', '');
    // phoneNumber = phoneNumber.replace(/[^0-9]/g, '');
  }
  return phoneNumber;
}

checkValidEmail(String email) {
  String textEmail = email.trim();
  Pattern pattern =
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
  RegExp regex = RegExp(pattern as String);
  if (!regex.hasMatch(textEmail))
    return AppStrings.getString(AppStrings.invalidEmail);
  else
    return null;
}

checkEmptyEmail(String email) {
  return email.length == 0 ? AppStrings.getString(AppStrings.emptyEmail) : null;
}

checkEmptyAndValidEmail(String email) {
  if (checkEmptyEmail(email) != null) {
    return checkEmptyEmail(email);
  }
  if (checkValidEmail(email) != null) {
    return checkValidEmail(email);
  }
  return null;
}

checkInValidPhone(String phone) {
// Loại bỏ khoảng trắng thừa
  phone = phone.trim();

  // Regex kiểm tra số điện thoại Việt Nam (10 chữ số, bắt đầu bằng 03, 05, 07, 08, 09)
  final RegExp phoneRegex = RegExp(r'^(03|05|07|08|09)\d{8}$');

  if (phone.isEmpty) {
    return true;
  }

  if (!phoneRegex.hasMatch(phone)) {
    return true;
  }
  return false;
}

String checkIsNullOrEmptyToString(Object? o) {
  if (o != null) {
    return o.toString();
  }
  return "";
}

bool isNullOrEmpty(String? value) {
  return value == null || value.trim().isEmpty;
}

bool isNullEmpty(Object? o) => o == null || "" == o;

bool isNullEmptyOrFalse(Object? o) => o == null || false == o || "" == o;

bool isNullEmptyFalseOrZero(Object? o) => o == null || false == o || 0 == o || "" == o || "0" == o;

bool isNumeric(dynamic s) {
  String sConvert = s.toString();
  if (sConvert == null) {
    return false;
  }
  return (double.tryParse(sConvert) != null || int.tryParse(sConvert) != null);
}

int convertStringToInt(String? data) {
  if ((data != null) && (data.isNotEmpty) && isNumeric(data)) {
    return int.parse(data);
  }else {
    return 0;
  }
}

buildFormatBankNumber(String bankNumber) {
  StringBuffer formattedString = StringBuffer();

  int length = bankNumber.length;
  int index = 0;
  while (index < length) {
    if (index + 4 <= length) {
      formattedString.write(bankNumber.substring(index, index + 4));
      index += 4;
      if (index < length) {
        formattedString.write('  ');
      }
    } else {
      formattedString.write(bankNumber.substring(index));
      break;
    }
  }

  return formattedString.toString();
}

bool checkMinMaxAmount(String amount, String minAmount, String maxAmount) {
  if (isNullEmpty(amount) || isNullEmpty(minAmount) || isNullEmpty(maxAmount)) {
    return false;
  }
  amount = convertMoney(amount);
  return int.parse(minAmount) <= int.parse(amount) && int.parse(amount) <= int.parse(maxAmount);
}

String convertMoney(String amountMoney) {
  return amountMoney.replaceAll("[^\\d]", "").trim();
}