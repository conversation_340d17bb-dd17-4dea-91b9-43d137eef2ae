import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/screen/detail_promo/promo_detail_controller.dart';
import 'package:card_promo/screen/detail_promo/promo_detail_screen.dart';
import 'package:card_promo/screen/favourite/favourite_controller.dart';
import 'package:card_promo/screen/favourite/favourite_screen.dart';
import 'package:card_promo/screen/login/login_controller.dart';
import 'package:card_promo/screen/login/login_screen.dart';
import 'package:card_promo/screen/map/map_controller.dart';
import 'package:card_promo/screen/map/map_screen.dart';
import 'package:card_promo/screen/merchant_promo/merchant_promo_screen.dart';
import 'package:card_promo/screen/regist_card/regist_card_controller.dart';
import 'package:card_promo/screen/regist_card/regist_card_screen.dart';
import 'package:card_promo/screen/webview/webview_controller.dart';
import 'package:card_promo/screen/webview/webview_screen.dart';
import 'package:card_promo/web/screen/merchant/merchant_controller.dart';
import 'package:card_promo/web/screen/portal_controller.dart';
import 'package:card_promo/web/screen/portal_screen.dart';
import 'package:card_promo/web/screen/promo_controller.dart';
import 'package:card_promo/web/screen/promo_screen.dart';
import 'package:get/get.dart';

import '../screen/account/account_screen.dart';
import '../screen/contribute/contribute_controller.dart';
import '../screen/contribute/contribute_screen.dart';
import '../screen/home/<USER>';
import '../screen/home/<USER>';
import '../screen/merchant_promo/merchant_promo_controller.dart';
import '../web/screen/merchant/merchant_screen.dart';


class AppRoute {
  static final pages = [
    GetPage(
        name: AppNameRoute.login_screen,
        page: () => LoginScreen(),
        binding: LoginBinding()),
    GetPage(
        name: AppNameRoute.home_screen,
        page: () => HomeScreen(),
        binding: HomeBinding()),
    GetPage(
        name: AppNameRoute.promo_detai_screen,
        page: () => PromoDetailScreen(),
        binding: PromoDetailBiding(),
      transition: Transition.zoom,
      transitionDuration: Duration(milliseconds: 200),
    ),
    GetPage(
        name: AppNameRoute.account_screen,
        page: () => AccountScreen(),
        transition: Transition.cupertino,
        binding: AccountBinding()),
    GetPage(
        name: AppNameRoute.favourite_screen,
        page: () => FavouriteScreen(),
        transition: Transition.cupertino,
        binding: FavouriteBinding()),
    GetPage(
        name: AppNameRoute.webview_screen,
        page: () => WebviewScreen(),
        transition: Transition.cupertino,
        binding: WebviewBinding()),
    GetPage(
        name: AppNameRoute.map_screen,
        page: () => MapScreen(),
        transition: Transition.cupertino,
        binding: MapBinding()),
    GetPage(
        name: AppNameRoute.contribute_screen,
        page: () => ContributeScreen(),
        transition: Transition.cupertino,
        binding: ContributeBinding()),
    GetPage(
        name: AppNameRoute.regist_card_screen,
        page: () => RegistCardScreen(),
        transition: Transition.cupertino,
        binding: RegistCardBindings()),
    GetPage(
        name: AppNameRoute.portalScreen,
        page: () => PortalScreen(),
        transition: Transition.cupertino,
        binding: PortalBinding()),

    GetPage(
        name: AppNameRoute.promoScreen,
        page: () => PromoScreen(),
        transition: Transition.cupertino,
        binding: PromoBinding()),
    GetPage(
        name: AppNameRoute.merchant_promo_screen,
        page: () => MerchantPromoScreen(),
        transition: Transition.cupertino,
        binding: MerchantPromoBinding()),

  ];
}

class AppNameRoute {
  static const String login_screen = '/login_screen';
  static const String home_screen = '/home_screen';
  static const String promo_detai_screen = '/promo_detai_screen';
  static const String account_screen = '/account_screen';
  static const String favourite_screen = '/favourite_screen';
  static const String webview_screen = '/webview_screen';
  static const String map_screen = '/map_screen';
  static const String contribute_screen = '/contribute_screen';
  static const String regist_card_screen = '/regist_card_screen';
  static const String merchant_promo_screen = '/merchant_promo_screen';



  static const String portalScreen = '/portalScreen';
  static const String promoScreen = '/promoScreen';
}
