import 'dart:convert';
import 'dart:math';

import 'package:card_promo/util/ufo_logger.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../widget/base_dialog.dart';
import 'api_constant.dart';
import 'app_validation.dart';

class AppUtils {
  static int? random(min, max) {
    var rn = new Random();
    return min + rn.nextInt(max - min);
  }

  static void hideKeyboard(context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
  }

  static String convertPhoneToOrigin(String phone) {
    String phoneNumber = phone.trim();
    if (phoneNumber.length != 0) {
      phoneNumber = phoneNumber.replaceFirst('(+84)', '0');
      phoneNumber = phoneNumber.replaceFirst('+84', '0');
      // phoneNumber = phoneNumber.replace('0084', '0');
      phoneNumber = phoneNumber.replaceAll(' ', '');
      // phoneNumber = phoneNumber.replace(/[^0-9]/g, '');
    }
    return phoneNumber;
  }

  static String? formatPhoneNumber(String? text) {
    String? filterText = text;
    if (isNullEmpty(filterText)) return '';
    if (filterText!.length < 2) return filterText;
    filterText = filterText.replaceAll(' ', '');
    String firstChars = filterText.substring(0, 2);
    if (firstChars == '09' || firstChars == '08' || firstChars == '07' || firstChars == '03' || firstChars == '05') {
      if (filterText.length > 3) {
        filterText = filterText.substring(0, 3) + ' ' + filterText.substring(3);
      }
      if (filterText.length > 7) {
        filterText = filterText.substring(0, 7) + ' ' + filterText.substring(7);
      }
    }
    return filterText.trim();
  }

  static void openCallPhoneSupport() async {
    const url = 'tel:${ApiConstant.SUPPORT_PHONE}';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  static void launchMailHotroMpos(bool isReport) async {
    String toMailId = ApiConstant.SUPPORT_EMAIL;
    String subject = isReport ? 'Góp ý' : 'Liên hệ';
    String body = '';
    launchMail(toMailId, subject, body);
  }

  static Future<void> launchMail(String toMailId, String subject, String body) async {
    final Uri _emailLaunchUri = Uri(
        scheme: 'mailto', path: toMailId,
    );

    if (await canLaunchUrl(_emailLaunchUri)) {
      await launchUrl(_emailLaunchUri);
    } else {
      throw 'Could not launch $_emailLaunchUri';
    }
  }

  // static showDialogError(BuildContext context, String? errText,
  //     {String? title, Function? onPressButton, String? image, TextAlign? descriptionTextAlign}) {
  //   showDialog(
  //       context: context,
  //       builder: (BuildContext context) {
  //         return DialogAlert(
  //           image: image ?? null,
  //           onPress1stButton: onPressButton ?? null,
  //           title: title ?? AppStrings.getString(AppStrings.errorTitle),
  //           description: errText,
  //           descriptionTextAlign: descriptionTextAlign ?? null,
  //           text1stButton: AppStrings.getString(AppStrings.close),
  //         );
  //       });
  // }

  static showDialogAlert(BuildContext context,
      {String? title,
      String? description,
      String? text1stButton,
      String? text2ndButton,
      Function? onPress1stButton,
      Function? onPress2ndButton,
      bool? isTwoButton,
      Widget? widgetDescription, String? type,
      }) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return BaseDialog(
            title: title,
            desc: description,
            nameBtn1st: text1stButton,
            nameBtn2nd: text2ndButton,
            onPress1stButton: onPress1stButton,
            onPress2ndButton: onPress2ndButton,
            isTwoButton: isTwoButton,
            widgetDesc: widgetDescription,
            type: type,
          );
        });
  }

  static String formatCurrency(dynamic number) {
    if (isNullEmptyFalseOrZero(number) || !isNumeric(number)) {
      return '0';
    }
    dynamic numberConvert;
    if (number is String) {
      numberConvert = int.tryParse(number) ?? double.tryParse(number);
    } else {
      numberConvert = number;
    }
    return NumberFormat("#,###", "vi_VN").format(numberConvert ?? 0);
  }

  static String format4CharNumber(String text) {
    String filterText = text;
    if (isNullEmpty(filterText)) return '';
    final value = filterText.replaceAllMapped(RegExp(r".{4}"), (match) => "${match.group(0)} ");
    return value.trim();
  }

  static T? parseResponse<T>(BuildContext context, String response, T Function(Map<String, dynamic>) fromJson, {bool isShowErr = true}) {
    try {
      var map = jsonDecode(response);
      if (map['code'] == ApiConstant.DO_SERVICE_SUCCESS &&
          map['result'] != null) {
        return fromJson(map['result']);
      } else {
        AppUtils.showDialogAlert(
          context,
          type: BaseDialog.TYPE_ERROR,
          title: 'Có lỗi xảy ra',
          description:
              '${map['code']}- ${map['message'] ?? 'Không thể lấy được dữ liệu, vui lòng thử lại!'}',
        );
      }
    } catch (e) {
      UfoLogger().writeLog(
          action: ApiConstant.LOGGER_TYPE_RESPONSE, '${e.toString()}');
      isShowErr ? AppUtils.showDialogAlert(
        context,
        type: BaseDialog.TYPE_ERROR,
        title: 'Có lỗi xảy ra',
        description: '${e}}',
      ) : null;
    }
    return null;
  }

  static String formatPan(String pan) {
    if (pan == null || pan.length < 10) {
      return "**** **** **** ****";
    }
    int length = pan.length;
    String firstSix = pan.substring(0, 6);
    String lastFour = pan.substring(length - 4);
    int maskedCount = length - 10;

    StringBuffer masked = StringBuffer();
    for (int i = 0; i < maskedCount; i++) {
      masked.write("*");
    }

    // Ghép các phần lại với nhau (không có khoảng cách)
    String combined = firstSix + masked.toString() + lastFour;

    // Chia chuỗi kết quả thành các nhóm 4 ký tự, cách nhau bởi khoảng trắng
    StringBuffer spaced = StringBuffer();
    for (int i = 0; i < combined.length; i++) {
      spaced.write(combined[i]);
      // Nếu đủ 4 ký tự trong 1 nhóm và chưa phải ký tự cuối cùng, thêm khoảng trắng
      if ((i + 1) % 4 == 0 && i != combined.length - 1) {
        spaced.write(" ");
      }
    }
    return spaced.toString();
  }

  static String getLoginData() {
    Map<String, dynamic> jsonData = {
      "code": 1000,
      "message": null,
      "result": {
        "username": "<EMAIL>",
        "lastTimeLogin": 1739586044780,
        "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.******************************************************************************.PVndER71U1-cJZzLjpUBILaaX_RA8vYkAcBt83C9u53uqm48IGMmNjlD6Oz6SeDLxwthaodSqobBt3yrXCL0V7Qpg-kIm-u7Mr3-4ujbdXUpIe0_EQT9uNdIHBdVQb28n1lckFPKw4FZqkaqFyY06vSgHsGdZYzuzD997T1G97F8QkVDw9dH0jzJNdb8AUoOA_IZRAg0D8487pCRszv0g-iAxALaeuIQC8Lmi_UnuLhRInIyns01muvnciZAn9_WEF5FxH6vAXpcTSmUfbQXwk5Iiel9-WaZHYBhqkcpUmql8dTLYXiP6m0HlW-lXfYzEg1J4VtFdktJCKhSwZlXFPAZHMfs5sK8iWXLfmRs1WJgDrijrSvOZA6xL9T_gWOPtzSNis-9H9xQwPEkfh4T2FeKGM3r-sw3xtzWsCiwrBh4plOJ2Sn9IGvZpqSMNvMFLXbLuTzMUgDIUL0TZhgaOAMK-ExEh9So4GtY4VRJNQFkX-VXky0amT2aQPl3G1UeXbBAoZBj432DSVWOpamP6yU1znwaolSDQoxgtAGbHejsRVyoaixQGgQ4tl5s9U5qUam8GxfELHs7UXbUNDuUfqvyrIrv3nfz65JXSYqUgAZoOG0iCuKYBZWEuxFIOpzYlIULdu7mRctEQz-YdaVucS2Ob6B1zevT8VwWVlnKqrI",
        "cardDetails": [
          {
            "bank": "TECHCOMBANK",
            "pan": "***************",
            "expireDate": "23/34",
            "cardHolderName": "NGUYEN XUAN THUAN",
            "cardType": "VISA",
            "bankCode": "TCB",
            "createdDate": null,
            "updatedDate": null
          }
        ],
        "uid": "dd206f7e-ab7f-46dc-89e6-c92b722acae9"
      }
    };

    // Chuyển Map thành String JSON
    String jsonString = jsonEncode(jsonData);
    return jsonString;
  }

}
