import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:card_promo/data/model/config.dart';
import 'package:card_promo/data/model/user.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:intl/intl.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'data/model/bank_data.dart';
import 'data/model/banner.dart';
import 'data/model/login_model.dart';
import 'data/model/user_info.dart';
import 'util/app_utils.dart';
import 'util/local_storage.dart';

class MyAppController extends SuperController {
  final _storage = FlutterSecureStorage();
  final _localStorage = LocalStorage();

  RxBool loading = false.obs;
  String? appVersion = '';
  String? buildNumber = '';
  Timer? _timer;

  RxString currentLanguageApp = ''.obs;
  RxBool isEnglish = false.obs;

  LoginModel? loginModel;

  RxBool showAds = false.obs;
  RxList<BannerData> listBanner = <BannerData>[].obs;
  RxList<BankData> listBank = <BankData>[].obs;

  RxBool isLoggedIn = false.obs;

  Rx<UserInfo> user = UserInfo().obs;
  Rx<Config> config = Config().obs;

  @override
  void onInit() {
    super.onInit();
    checkLoginStatus();
    checkLanguage();
  }

  Future<void> checkLanguage() async {
    String language = await _localStorage.getLanguageApp();
    if (language.isEmpty) {
      await _localStorage.setLanguageApp('vi');
      currentLanguageApp.value = 'vi';
      isEnglish.value = false;
    } else {
      currentLanguageApp.value = language;
      isEnglish.value = language == 'en';
    }
  }

  Future<void> checkLoginStatus() async {
    try {
      final String? userDataStr = await _localStorage.getData('facebook_user_data');
      if (userDataStr != null && userDataStr.isNotEmpty) {
        final Map<String, dynamic> userData = jsonDecode(userDataStr);
        user.value = UserInfo.fromJson(userData);
        isLoggedIn.value = true;
      }
    } catch (e) {
      print('Error checking login status: $e');
    }
  }

  Future<void> saveLoginData(Map<String, dynamic> userData) async {
    try {

      user.value.userId = userData['id'] ?? '';
      user.value.userName = userData['name'] ?? '';
      user.value.userAvatar = userData['picture']?['data']?['url'] ?? '';

      await _localStorage.saveData(
        'facebook_user_data',
        jsonEncode(user),
      );

      // idUser.value = userData['id'] ?? '';
      // userName.value = userData['name'] ?? '';
      // userAvatar.value = userData['picture']?['data']?['url'] ?? '';
      isLoggedIn.value = true;
    } catch (e) {
      print('Error saving login data: $e');
    }
  }

  Future<void> clearLoginData() async {
    try {
      await _localStorage.clear('facebook_user_data');
      user.value.clean();

      // idUser.value = '';
      // userName.value = '';
      // userAvatar.value = '';
      isLoggedIn.value = false;
    } catch (e) {
      print('Error clearing login data: $e');
    }
  }

  showLoading({bool autoHide = true}) {
    loading.value = true;
    if (autoHide) {
      _timer = Timer.periodic(Duration(seconds: 10), (timer) {
        if (loading.value == true) {
          hideLoading();
        }
      });
    }
  }

  hideLoading() {
    loading.value = false;
    if (_timer != null) {
      _timer!.cancel();
    }
  }

  @override
  void onDetached() {}

  @override
  void onInactive() {}

  @override
  void onPaused() {}

  @override
  void onResumed() {
    // _checkCacheLogError();
  }

  @override
  void onHidden() {
    // TODO: implement onHidden
  }
}