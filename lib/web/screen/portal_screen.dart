import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/screen/detail_promo/promo_detail_controller.dart';
import 'package:card_promo/screen/favourite/favourite_controller.dart';
import 'package:card_promo/web/screen/merchant/merchant_screen.dart';
import 'package:card_promo/web/screen/portal_controller.dart';
import 'package:card_promo/web/screen/promo_screen.dart';
import 'package:card_promo/web/screen/settings_screen.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../data/model/promo_detail.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../util/app_dimens.dart';
import '../../util/styles.dart';
import '../../widget/itemPromoWidget.dart';
import '../../widget/line_widget.dart';

class PortalScreen extends GetView<PortalController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Row(
        children: [
          // Menu cố định bên trái
          Container(
            width: 250,
            color: AppColors.background_white,
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.all(AppDimens.spaceMedium),
                  decoration: BoxDecoration(
                    color: AppColors.ufo_color_background_button,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: AppColors.white,
                        child: Icon(Icons.person, size: 40, color: AppColors.background_green),
                      ),
                      SizedBox(height: AppDimens.spaceMedium),
                      Text(
                        'Admin Portal',
                        style: style_S20_W600_WhiteColor,
                      ),
                      Text(
                        '<EMAIL>',
                        style: style_S14_W400_WhiteColor,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Obx(
                    () => ListView(
                      padding: EdgeInsets.zero,
                      children: [
                        _buildMenuItem(
                          icon: Icons.dashboard,
                          title: 'Tổng quan',
                          isSelected: controller.currentTab.value == PortalTab.dashboard,
                          onTap: () => controller.changePage('dashboard'),
                        ),
                        _buildMenuItem(
                          icon: Icons.local_offer,
                          title: 'Khuyến mãi',
                          isSelected: controller.currentTab.value == PortalTab.promos,
                          onTap: () => controller.changePage('promo'),
                        ),
                        _buildMenuItem(
                          icon: Icons.store,
                          title: 'Merchant',
                          isSelected: controller.currentTab.value == PortalTab.merchants,
                          onTap: () => controller.changePage('merchant'),
                        ),
                        _buildMenuItem(
                          icon: Icons.people,
                          title: 'Người dùng',
                          isSelected: controller.currentTab.value == PortalTab.users,
                          onTap: () => controller.changePage('users'),
                        ),
                        _buildMenuItem(
                          icon: Icons.credit_card,
                          title: 'Yêu cầu mở thẻ',
                          isSelected: controller.currentTab.value == PortalTab.cardRequests,
                          onTap: () => controller.changePage('card_requests'),
                        ),
                        Divider(),
                        _buildMenuItem(
                          icon: Icons.settings,
                          title: 'Cài đặt',
                          isSelected: controller.currentTab.value == PortalTab.settings,
                          onTap: () => controller.changePage('settings'),
                        ),
                        _buildMenuItem(
                          icon: Icons.logout,
                          title: 'Đăng xuất',
                          isSelected: false,
                          onTap: () {
                            // TODO: Xử lý đăng xuất
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Nội dung bên phải
          Expanded(
            child: Column(
                children: [
                  Expanded(
                  child: Obx(() {
                    switch (controller.currentTab.value) {
                      case PortalTab.dashboard:
                        return SingleChildScrollView(
                          child: _buildDashboardContent(),
                        );
                      case PortalTab.promos:
                        return PromoScreen();
                      case PortalTab.merchants:
                        return MerchantScreen();
                      case PortalTab.users:
                        return Container();
                      case PortalTab.cardRequests:
                        return Container();
                      case PortalTab.settings:
                        return SettingsScreen();
                      default:
                        return SizedBox.shrink();
                    }
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? AppColors.primary : Colors.grey,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? AppColors.primary : Colors.black,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      onTap: onTap,
    );
  }

  Widget _buildDashboardContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildStats(),
        const SizedBox(height: 24),
        _buildTopContributors(),
        const SizedBox(height: 24),
        _buildFeaturedPromos(),
      ],
    );
  }

  Widget _buildStats() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thống kê',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 4,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildStatCard('Tổng số người dùng', '1', Icons.people),
            _buildStatCard('Tổng số thương hiệu', '3', Icons.store),
            _buildStatCard('Tổng số khuyến mãi', '1000', Icons.local_offer),
            _buildStatCard('Yêu cầu mở thẻ', '10', Icons.credit_card),
          ],
        ),
      ],
    );
  }

  Widget _buildTopContributors() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Top người đóng góp',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 1,
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(
                  child: Text('${index + 1}'),
                ),
                title: Text('Admin'),
                subtitle: Text('${(index + 1) * 10} khuyến mãi'),
                trailing: Text(
                  '${(index + 1) * 100} điểm',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedPromos() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Khuyến mãi nổi bật',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            itemBuilder: (context, index) {
              return ListTile(
                leading: const Icon(Icons.local_offer),
                title: Text('Khuyến mãi ${index + 1}'),
                subtitle: Text('Mô tả khuyến mãi ${index + 1}'),
                trailing: const Icon(Icons.arrow_forward_ios),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(AppDimens.spaceMedium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 40, color: AppColors.primary),
            Text(
              value,
              style: style_S24_W600_BlackColor,
            ),
            Text(
              title,
              style: style_S14_W400_GreyColor,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
