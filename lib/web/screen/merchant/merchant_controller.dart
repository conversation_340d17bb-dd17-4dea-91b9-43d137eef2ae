import 'dart:convert';

import 'package:card_promo/util/app_utils.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:card_promo/data/model/promo_detail.dart';
import 'package:card_promo/data/model/merchant.dart';
import 'package:card_promo/util/api_constant.dart';

import '../../../data/provider/base_service.dart';
import '../../../res/color/app_colors.dart';
import '../../../util/app_dimens.dart';
import '../../widget_create_merchant.dart';


class MerchantBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => MerchantController(), fenix: false);
  }
}

class MerchantController extends GetxController {
  late BuildContext context;

  RxList<Merchant> merchants = <Merchant>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadMerchants();
  }

  void searchMerchant(String query) {
    merchants.value = merchants.where((merchant) {
      return merchant.name.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  void onAddMerchant() {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final phoneController = TextEditingController();
    final locations = <MerchantLocation>[].obs;

    Get.dialog(
      Dialog(
        child: Container(
          color: AppColors.white,
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width * 0.9, // 90% chiều rộng màn hình
          height: MediaQuery.of(context).size.height * 0.8, // 80% chiều cao màn hình
          child: WidgetCreateMerchant(merchants: merchants,)
        ),
      ),
    );
  }

  void doAddMerchant(var params) async {
    try {
      final result = await BaseService().postDataWeb(ApiConstant.urlAddMerchant, params);
      if (result['success']) {
        final data = result['data'];
        Merchant? merchant = AppUtils.parseResponse<Merchant>(
            context, jsonEncode(data), Merchant.fromJson);
        if (merchant != null) {
          merchants.add(merchant);
          Get.snackbar(
            'Thành công',
            'Đã thêm merchant thành công',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể thêm merchant: ${result['error']}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
      merchants.refresh();
    } catch (e) {
      print('Error adding merchant: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể thêm merchant',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void loadMerchants() async {
    merchants..clear();
    merchants.refresh();
    try {
      final result = await BaseService().getDataWeb(ApiConstant.urlGetAllMerchant);
      print('Load merchants result: $result');
      if (result['success']) {
        final data = result['data'];
        if (data['result'] != null) {
          List<Merchant> merchantList = (data['result'] as List<dynamic>)
              .map((item) => Merchant.fromJson(item as Map<String, dynamic>))
              .toList();
          print('Loaded ${merchantList.length} merchants');
          merchants.value = merchantList;
        } else {
          print('No merchants found in response');
          merchants.value = [];
        }
        merchants.refresh();
      } else {
        print('Error loading merchants: ${result['error']}');
        Get.snackbar(
          'Lỗi',
          'Không thể tải danh sách merchant: ${result['error']}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print('Error loading merchants: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể tải danh sách merchant',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void onEditMerchant(Merchant merchant) {
    final nameController = TextEditingController(text: merchant.name);
    final emailController = TextEditingController(text: merchant.email);
    final phoneController = TextEditingController(text: merchant.phone);
    final locations = merchant.locations.obs;

    Get.dialog(
      AlertDialog(
        title: Text('Chỉnh sửa merchant'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: 'Tên merchant',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: AppDimens.spaceMedium),
              TextField(
                controller: emailController,
                decoration: InputDecoration(
                  labelText: 'Email',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: AppDimens.spaceMedium),
              TextField(
                controller: phoneController,
                decoration: InputDecoration(
                  labelText: 'Số điện thoại',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: AppDimens.spaceMedium),
              Divider(),
              Text('Danh sách chi nhánh', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: AppDimens.spaceMedium),
              Obx(() => Column(
                children: locations.map((location) => Card(
                  margin: EdgeInsets.only(bottom: AppDimens.spaceSmall),
                  child: ListTile(
                    title: Text(location.branchName ?? ''),
                    subtitle: Text(location.address ?? ''),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: Icon(Icons.edit),
                          onPressed: () => showEditLocationDialog(locations, location),
                        ),
                        IconButton(
                          icon: Icon(Icons.delete),
                          onPressed: () => locations.remove(location),
                        ),
                      ],
                    ),
                  ),
                )).toList(),
              )),
              SizedBox(height: AppDimens.spaceMedium),
              ElevatedButton.icon(
                onPressed: () => showAddLocationDialog(locations),
                icon: Icon(Icons.add_location),
                label: Text('Thêm chi nhánh'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.isEmpty || emailController.text.isEmpty || phoneController.text.isEmpty) {
                Get.snackbar(
                  'Lỗi',
                  'Vui lòng điền đầy đủ thông tin',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final params = {
                'id': merchant.id,
                'name': nameController.text,
                'email': emailController.text,
                'phone': phoneController.text,
                'locations': locations.map((loc) => loc.toJson()).toList(),
              };
              await updateMerchant(params);
              Get.back();
            },
            child: Text('Lưu'),
          ),
        ],
      ),
    );
  }

  void showAddLocationDialog(RxList<MerchantLocation> locations) {
    final branchNameController = TextEditingController();
    final addressController = TextEditingController();
    final latController = TextEditingController();
    final longController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text('Thêm chi nhánh'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: branchNameController,
              decoration: InputDecoration(
                labelText: 'Tên chi nhánh',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              controller: addressController,
              decoration: InputDecoration(
                labelText: 'Địa chỉ',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: latController,
                    decoration: InputDecoration(
                      labelText: 'Vĩ độ',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                SizedBox(width: AppDimens.spaceMedium),
                Expanded(
                  child: TextField(
                    controller: longController,
                    decoration: InputDecoration(
                      labelText: 'Kinh độ',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              if (branchNameController.text.isEmpty ||
                  addressController.text.isEmpty ||
                  latController.text.isEmpty ||
                  longController.text.isEmpty) {
                Get.snackbar(
                  'Lỗi',
                  'Vui lòng điền đầy đủ thông tin',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final lat = double.tryParse(latController.text);
              final long = double.tryParse(longController.text);

              if (lat == null || long == null) {
                Get.snackbar(
                  'Lỗi',
                  'Vĩ độ và kinh độ phải là số',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              locations.add(MerchantLocation(
                branchName: branchNameController.text,
                address: addressController.text,
                location: GeoJsonPoint(
                  x: lat,
                  y: long,
                  type: 'Point',
                  coordinates: [long, lat],
                ),
              ));
              Get.back();
            },
            child: Text('Thêm'),
          ),
        ],
      ),
    );
  }

  void showEditLocationDialog(RxList<MerchantLocation> locations, MerchantLocation location) {
    final branchNameController = TextEditingController(text: location.branchName);
    final addressController = TextEditingController(text: location.address);
    final latController = TextEditingController(text: location.location?.x.toString());
    final longController = TextEditingController(text: location.location?.y.toString());

    Get.dialog(
      AlertDialog(
        title: Text('Chỉnh sửa chi nhánh'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: branchNameController,
              decoration: InputDecoration(
                labelText: 'Tên chi nhánh',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: addressController,
                    decoration: InputDecoration(
                      labelText: 'Địa chỉ',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                SizedBox(width: 10,),
                ElevatedButton(
                  onPressed: () {
                    onPressGetLocation(latController, longController);
                  },
                  child: Text('Lấy vị trí'),
                ),
              ],
            ),
            SizedBox(height: AppDimens.spaceMedium),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: latController,
                    decoration: InputDecoration(
                      labelText: 'Vĩ độ',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                SizedBox(width: AppDimens.spaceMedium),
                Expanded(
                  child: TextField(
                    controller: longController,
                    decoration: InputDecoration(
                      labelText: 'Kinh độ',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              if (branchNameController.text.isEmpty ||
                  addressController.text.isEmpty ||
                  latController.text.isEmpty ||
                  longController.text.isEmpty) {
                Get.snackbar(
                  'Lỗi',
                  'Vui lòng điền đầy đủ thông tin',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final lat = double.tryParse(latController.text);
              final long = double.tryParse(longController.text);

              if (lat == null || long == null) {
                Get.snackbar(
                  'Lỗi',
                  'Vĩ độ và kinh độ phải là số',
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              final index = locations.indexOf(location);
              if (index != -1) {
                locations[index] = MerchantLocation(
                  branchName: branchNameController.text,
                  address: addressController.text,
                  location: GeoJsonPoint(
                    x: lat,
                    y: long,
                    type: 'Point',
                    coordinates: [long, lat],
                  ),
                );
              }
              Get.back();
            },
            child: Text('Lưu'),
          ),
        ],
      ),
    );
  }

  Future<void> updateMerchant(Map<String, dynamic> params) async {
    try {
      final result = await BaseService().postDataWeb(ApiConstant.urlUpdateMerchant, params);

      if (result['success']) {
        final data = result['data'];
        Merchant? merchant = AppUtils.parseResponse<Merchant>(
            context, jsonEncode(data), Merchant.fromJson);
        if (merchant != null) {
          final index = merchants.indexWhere((m) => m.id == merchant.id);
          if (index != -1) {
            merchants.value[index] = merchant;
          }
          Navigator.pop(context);
          Get.snackbar(
            'Thành công',
            'Đã cập nhật merchant thành công',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể cập nhật merchant: ${result['error']}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      print('Error updating merchant: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể cập nhật merchant',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> onDeleteMerchant(String id) async {
    Get.dialog(
      AlertDialog(
        title: Text('Xóa merchant'),
        content: Text('Bạn có chắc chắn muốn xóa merchant này?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              doDeleteMerchant(id);
              Get.back();
            },
            child: Text('Xóa'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  void doDeleteMerchant(String id) async {
    try {
      Map<String, dynamic> params = {
        'id': id
      };
      final result = await BaseService().postDataWeb('${ApiConstant.urlDeleteMc}', params);
      if (result['success']) {
        merchants.removeWhere((m) => m.id == id);
        Get.snackbar(
          'Thành công',
          'Đã xóa merchant thành công',
          snackPosition: SnackPosition.BOTTOM,
        );
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể xóa merchant: ${result['error']}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
      merchants.refresh();
      Get.back();
    } catch (e) {
      Get.back();
      print('Error deleting merchant: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể xóa merchant',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void onPressGetLocation(TextEditingController latController, TextEditingController longController) {
    latController.text = '123213.3';
    longController.text = '11111.3';
  }

} 