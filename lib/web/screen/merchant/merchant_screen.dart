import 'package:card_promo/data/model/bank_data.dart';
import 'package:card_promo/data/model/promo_detail.dart';
import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/util/app_dimens.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/web/screen/merchant/merchant_controller.dart';
import 'package:card_promo/web/screen/promo_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:card_promo/data/model/merchant.dart';
import 'package:intl/intl.dart';

class MerchantScreen extends GetView<MerchantController> {
  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '<PERSON>h sách <PERSON>',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Row(
            children: [
              ElevatedButton.icon(
                onPressed: () {
                  controller.onAddMerchant();
                },
                icon: const Icon(Icons.add),
                label: const Text('Thêm Merchant'),
              ),
              SizedBox(width: 10,),
              ElevatedButton.icon(
                onPressed: () {
                  controller.loadMerchants();
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Làm mới'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Obx(() => GridView.builder(
              padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 5),
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: controller.merchants.length,
                  itemBuilder: (context, index) {
                    final merchant = controller.merchants[index];
                    return Container(
                      margin: const EdgeInsets.symmetric(
                          vertical: 5, horizontal: 5),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black12,
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                child: Text(merchant.name[0].toUpperCase()),
                                backgroundColor: AppColors.primary,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  merchant.name,
                                  style: const TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ],
                          ),
                          Row(
                            children: [
                              Expanded(
                                  child: Column(
                                children: [
                                  const SizedBox(height: 8),
                                  Text('Email: ${merchant.email}'),
                                  Text('SĐT: ${merchant.phone}'),
                                  Text(
                                      'Số chi nhánh: ${merchant.locations.length}'),
                                  const SizedBox(height: 8),
                                ],
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                              )),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  IconButton(
                                    icon: const Icon(Icons.edit),
                                    onPressed: () {
                                      controller.onEditMerchant(merchant);
                                    },
                                  ),
                                  IconButton(
                                    icon: const Icon(Icons.delete),
                                    onPressed: () {
                                      controller
                                          .onDeleteMerchant(merchant.id);
                                    },
                                  ),
                                ],
                              ),
                            ],
                          )
                        ],
                      ),
                    );
                  },
                )),
          ),
        ],
      ),
    );
  }
}
