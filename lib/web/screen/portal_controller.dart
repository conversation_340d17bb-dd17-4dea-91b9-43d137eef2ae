import 'dart:convert';
import 'package:card_promo/web/screen/merchant/merchant_controller.dart';
import 'package:card_promo/web/screen/promo_controller.dart';
import 'package:http/http.dart' as http;
import 'package:card_promo/util/api_constant.dart';
import 'package:card_promo/util/local_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:card_promo/data/provider/base_service.dart';

import '../../app_controller.dart';

import '../../res/color/app_colors.dart';
import '../../util/RSAEncryption.dart';
import '../../util/app_dimens.dart';
import '../../data/model/bank_data.dart';
import '../../data/model/merchant.dart';
import '../../util/app_utils.dart';

enum PortalTab {
  dashboard,
  promos,
  merchants,
  users,
  cardRequests,
  settings
}

class PortalBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => PortalController(), fenix: false);
    Get.lazyPut(() => PromoController(), fenix: true);
    Get.lazyPut(() => MerchantController(), fenix: true);
  }
}

class PortalController extends GetxController {
  final MyAppController _appController = Get.find<MyAppController>();
  late BuildContext context;
  
  // Biến để lưu trữ dữ liệu
  final currentTab = PortalTab.dashboard.obs;
  final currentPage = 'dashboard'.obs;
  final stats = <String, int>{}.obs;
  final topContributors = <Map<String, dynamic>>[].obs;
  final featuredPromos = <Map<String, dynamic>>[].obs;
  final filteredMerchants = <Merchant>[].obs;
  final users = <Map<String, dynamic>>[].obs;
  final cardRequests = <Map<String, dynamic>>[].obs;
  final banks = <BankData>[].obs;
  final promoRequests = <Map<String, dynamic>>[].obs;

  final baseService = BaseService();

  @override
  void onInit() {
    super.onInit();

  }

  @override
  void onReady() async {
    super.onReady();
    String AESKey = await LocalStorage().getData(LocalStorage.KEY_UFO, '');
    if (AESKey.isEmpty) {
      final response = await BaseService().getDataWeb(ApiConstant.urlUfo);
      if (response['data'] != null) {
        String clearKey = RSAEncryption.decryptData(response['data']['ufo']);
        print('clearKey: $clearKey');
        await LocalStorage().saveData(LocalStorage.KEY_UFO, clearKey).then((value) {
          _loadDashboardData();
        });
      }
    }else {
      _loadDashboardData();
    }
  }

  void changePage(String page) {
    currentPage.value = page;
    switch (page) {
      case 'dashboard':
        currentTab.value = PortalTab.dashboard;
        break;
      case 'promo':
        currentTab.value = PortalTab.promos;
        break;
      case 'merchant':
        currentTab.value = PortalTab.merchants;
        break;
      case 'users':
        currentTab.value = PortalTab.users;
        break;
      case 'card_requests':
        currentTab.value = PortalTab.cardRequests;
        break;
      case 'settings':
        currentTab.value = PortalTab.settings;
        break;
    }
  }

  void _loadDashboardData() {
    // TODO: Gọi API để lấy dữ liệu thống kê
    // Sử dụng BaseService để gọi API
    stats.value = {
      'totalPromos': 100,
      'totalMerchants': 50,
      'totalUsers': 1000,
      'totalCardRequests': 20,
    };

    // TODO: Load top contributors
    topContributors.value = List.generate(5, (index) => {
      'name': 'Người dùng ${index + 1}',
      'contributions': (index + 1) * 10,
      'rank': index + 1,
    });

    // TODO: Load featured promos
    featuredPromos.value = List.generate(3, (index) => {
      'title': 'Khuyến mãi ${index + 1}',
      'description': 'Mô tả khuyến mãi',
      'views': (index + 1) * 100,
    });
  }

  void onSearchUser(String query) async {
    try {
      final result = await BaseService().getDataWeb(
        '${ApiConstant.urlSearchUser}?query=$query',
      );
      
      if (result['success']) {
        final data = result['data'];
        if (data['result'] != null) {
          users.value = (data['result'] as List<dynamic>)
              .map((item) => item as Map<String, dynamic>)
              .toList();
        }
      } else {
        print('Error searching users: ${result['error']}');
      }
    } catch (e) {
      print('Error searching users: $e');
    }
  }

}