import 'dart:convert';

import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/util/app_utils.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/web/screen/merchant/merchant_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:card_promo/data/model/promo_detail.dart';
import 'package:card_promo/data/model/merchant.dart';
import 'package:card_promo/util/api_constant.dart';

import '../../data/model/bank_data.dart';
import '../../data/model/paging.dart';
import '../../data/model/promo_response.dart';
import '../../data/provider/base_service.dart';
import '../../util/local_storage.dart';
import '../../widget/base_dialog.dart';

class PromoBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => PromoController(), fenix: false);
  }
}

class PromoController extends GetxController {

  late BuildContext context;

  final promos = <PromoDetail>[].obs;
  final filteredPromos = <PromoDetail>[].obs;
  final filteredMerchants = <Merchant>[].obs;
  
  // Filter variables
  Rx<BankData> selectedBankCode = BankData().obs;

  // final selectedBankCode = BankData(bankName: bankName, bankCode: bankCode, shortName: shortName, icon: icon).obs;
  final selectedCategory = PromoCategory.values.first.displayName.obs;
  final selectedStatus = true.obs;
  final currentPage = 0.obs;
  final pageSize = 10.obs;

  Rx<Paging> paging = Paging(pageIndex: 0, pageSize: 1, dataCount: 0).obs;

  // prompt create promo
  RxList<String> categoryCreatePromo = <String>[].obs;

  RxList<BankData> banksData = <BankData>[].obs;

  var promoOptionAmountReduces = <PromoOptionAmountReduce>[].obs;
  var promoOptionAmountRefund = <PromoOptionAmountRefund>[].obs;

  final minAmountControllers = <TextEditingController>[];
  final fixAmountControllers = <TextEditingController>[];
  final rateAmountControllers = <TextEditingController>[];
  final maxAmountControllers = <TextEditingController>[];

  final minAmountRefundControllers = <TextEditingController>[];
  final fixAmountRefundControllers = <TextEditingController>[];
  final rateAmountRefundControllers = <TextEditingController>[];
  final maxAmountRefundControllers = <TextEditingController>[];

  @override
  void onInit() async {
    super.onInit();
    loadPromos();
    fetchBanks();
  }

  void goToPage(int page) {
    print('$page');
    if (page <=0) {
      return;
    }
    currentPage.value = page-1;
    loadPromos();
  }

  Future<void> loadPromos() async {
    try {
      promos.value.clear();
      promos.refresh();
      final params = {
        'active': selectedStatus.value,
        'pageIndex': currentPage.value
      };

      final result = await BaseService().postDataWeb('${ApiConstant.urlGetPromo}', params);

      if (result['success']) {
        final data = result['data'];
        if (data['result'] != null) {
          PromoResponse? promoData = PromoResponse.fromJson(data['result']);

          paging.value = promoData.paging;
          promos.value = promoData.promoData;
          filteredPromos.value = promoData.promoData;
        }
      }
    } catch (e) {
      print('Error loading promos: $e');
    }
  }

  void searchMerchant(String query) {
    if (query.isEmpty) {
      filteredMerchants.value = Get.find<MerchantController>().merchants;
    } else {
      filteredMerchants.value = Get.find<MerchantController>().merchants.where((merchant) {
        return merchant.name.toLowerCase().contains(query.toLowerCase());
      }).toList();
    }
  }

  void applyFilters() {
    loadPromos();
  }

  void resetFilters() {
    selectedBankCode.value = banksData.isNotEmpty ? banksData.first : BankData();
    selectedCategory.value = '';
    selectedStatus.value = true;
    currentPage.value = 0;
    loadPromos();
  }

  Future<void> addPromo(PromoDetail promo) async {
    try {
      if (inValidDataPromo(promo)) {
        AppUtils.showDialogAlert(
          context,
          type: BaseDialog.TYPE_ERROR,
          title: 'Có lỗi xảy ra.',
          description: 'Kiểm tra lại dữ liệu',
        );
        return;
      }

      final result = await BaseService().postDataWeb(
        ApiConstant.urlGetMerchantCreatePromo,
        promo.toJson(),
      );
      if (result['success']) {
        Get.back();
        Get.snackbar("Thành công", "Thêm promo thành công",
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.success,
            colorText: AppColors.white);
        loadPromos();
      } else if (isNullOrEmpty(result['code']) && isNullOrEmpty(result['message'])) {
        Get.snackbar("Có lỗi xảy ra.", '${result['message']}. Mã lỗi: ${result['code']}',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error,
            colorText: AppColors.white);
      }
    } catch (e) {
      print('Error adding promo: $e');
    }
  }

  Future<void> updatePromo(PromoDetail promo) async {
    try {
      final result = await BaseService().postDataWeb(
        ApiConstant.urlUpdatePromo,
        promo.toJson(),
      );

      if (result['success']) {
        loadPromos();
        Get.back();
      }
    } catch (e) {
      print('Error updating promo: $e');
    }
  }

  Future<void> deletePromo(String id) async {
    try {
      final result = await BaseService().postDataWeb(
        '${ApiConstant.urlDeletePromo}?id=$id',
        {}
      );

      if (result['success']) {
        loadPromos();
      }
    } catch (e) {
      print('Error deleting promo: $e');
    }
  }

  void loadBankCode() async {
    var response = await BaseService().getDataWeb(ApiConstant.urlAllBank);
    if (response['success']) {
      final data = response['data'];
      if (data['result'] != null) {
        banksData.value = (data['result'] as List<dynamic>)
            .map((item) => BankData.fromJson(item as Map<String, dynamic>))
            .toList();
        banksData.refresh();
        await LocalStorage().saveData(LocalStorage.KEY_BANK_CONFIG, jsonEncode(banksData));
      }
    }
  }

  bool inValidDataPromo(PromoDetail promo) {
    if (isNullOrEmpty(promo.bankCode) ||
        isNullOrEmpty(promo.title) ||
        promo.scheme?.length == 0 ||
        promo.category?.length == 0) {
      return true;
    }
    return false;
  }

  void fetchBanks() async {
    String banks = await LocalStorage().getData(LocalStorage.KEY_BANK_CONFIG, '');
    if (banks.isNotEmpty) {
      banksData.value.addAll(List<BankData>.from(jsonDecode(banks).map((x) => BankData.fromJson(x))));
      banksData.refresh();
    }else {
      loadBankCode();
    }
  }
}