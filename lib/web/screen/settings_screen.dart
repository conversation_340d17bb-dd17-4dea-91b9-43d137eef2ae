import 'package:card_promo/data/model/bank_data.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../res/color/app_colors.dart';
import '../../util/app_dimens.dart';
import '../../util/styles.dart';
import 'portal_controller.dart';

class SettingsScreen extends GetView<PortalController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Cài đặt', style: style_S20_W600_WhiteColor),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimens.spaceMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('Quản lý Banner'),
            _buildBannerSettings(),
            SizedBox(height: AppDimens.spaceLarge),
            _buildSectionTitle('Quản lý <PERSON>ông báo'),
            _buildNotificationSettings(),
            SizedBox(height: AppDimens.spaceLarge),
            _buildSectionTitle('Quản lý Ngân hàng'),
            _buildBankSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppDimens.spaceMedium),
      child: Text(
        title,
        style: style_S20_W600_BlackColor,
      ),
    );
  }

  Widget _buildBannerSettings() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppDimens.spaceMedium),
        child: Column(
          children: [
            ListTile(
              leading: Icon(Icons.add_photo_alternate),
              title: Text('Thêm banner mới'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Xử lý thêm banner
              },
            ),
            Divider(),
            ListTile(
              leading: Icon(Icons.list),
              title: Text('Danh sách banner'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Xử lý xem danh sách banner
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppDimens.spaceMedium),
        child: Column(
          children: [
            ListTile(
              leading: Icon(Icons.notifications),
              title: Text('Tạo thông báo mới'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showCreateNotificationDialog();
              },
            ),
            Divider(),
            ListTile(
              leading: Icon(Icons.history),
              title: Text('Lịch sử thông báo'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                // TODO: Xử lý xem lịch sử thông báo
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankSettings() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(AppDimens.spaceMedium),
        child: Column(
          children: [
            ListTile(
              leading: Icon(Icons.add_business),
              title: Text('Thêm ngân hàng mới'),
              trailing: Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showAddBankDialog();
              },
            ),
            Divider(),
            Obx(() => ListView.builder(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemCount: controller.banks.length,
              itemBuilder: (context, index) {
                final bank = controller.banks[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundImage: NetworkImage(bank.icon ?? ''),
                  ),
                  title: Text(bank.bankName ?? ''),
                  subtitle: Text('Mã: ${bank.bankCode}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: Icon(Icons.edit, color: AppColors.primary),
                        onPressed: () {
                          _showEditBankDialog(bank);
                        },
                      ),
                      IconButton(
                        icon: Icon(Icons.delete, color: AppColors.error),
                        onPressed: () {
                          _showDeleteBankDialog(bank);
                        },
                      ),
                    ],
                  ),
                );
              },
            )),
          ],
        ),
      ),
    );
  }

  void _showAddBankDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Thêm ngân hàng mới'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Tên ngân hàng',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'Mã ngân hàng',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'Tên viết tắt',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'URL icon',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Xử lý thêm ngân hàng
              Get.back();
            },
            child: Text('Thêm'),
          ),
        ],
      ),
    );
  }

  void _showEditBankDialog(BankData bank) {
    Get.dialog(
      AlertDialog(
        title: Text('Chỉnh sửa ngân hàng'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Tên ngân hàng',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(text: bank.bankName),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'Mã ngân hàng',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(text: bank.bankCode),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'Tên viết tắt',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(text: bank.shortName),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'URL icon',
                border: OutlineInputBorder(),
              ),
              controller: TextEditingController(text: bank.icon),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Xử lý cập nhật ngân hàng
              Get.back();
            },
            child: Text('Lưu'),
          ),
        ],
      ),
    );
  }

  void _showDeleteBankDialog(BankData bank) {
    Get.dialog(
      AlertDialog(
        title: Text('Xóa ngân hàng'),
        content: Text('Bạn có chắc chắn muốn xóa ngân hàng ${bank.bankName}?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Xử lý xóa ngân hàng
              Get.back();
            },
            child: Text('Xóa'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
            ),
          ),
        ],
      ),
    );
  }

  void _showCreateNotificationDialog() {
    Get.dialog(
      AlertDialog(
        title: Text('Tạo thông báo mới'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Tiêu đề',
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium),
            TextField(
              decoration: InputDecoration(
                labelText: 'Nội dung',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            SizedBox(height: AppDimens.spaceMedium),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: 'Loại thông báo',
                border: OutlineInputBorder(),
              ),
              items: [
                DropdownMenuItem(value: 'all', child: Text('Tất cả người dùng')),
                DropdownMenuItem(value: 'specific', child: Text('Người dùng cụ thể')),
              ],
              onChanged: (value) {
                // TODO: Xử lý chọn loại thông báo
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Xử lý gửi thông báo
              Get.back();
            },
            child: Text('Gửi'),
          ),
        ],
      ),
    );
  }
} 