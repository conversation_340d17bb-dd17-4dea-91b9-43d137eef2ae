import 'package:card_promo/data/model/bank_data.dart';
import 'package:card_promo/data/model/promo_detail.dart';
import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/util/app_dimens.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/web/screen/promo_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:card_promo/data/model/merchant.dart';
import 'package:intl/intl.dart';

import '../../util/api_constant.dart';
import '../../widget/common_text_field.dart';

class PromoScreen extends GetView<PromoController> {
  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Container(
      padding: EdgeInsets.all(AppDimens.spaceMedium),
      color: Colors.grey[100],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          <PERSON><PERSON><PERSON><PERSON>(height: AppDimens.spaceMedium),
          _buildFilters(),
          Si<PERSON>Box(height: AppDimens.spaceMedium),
          Expanded(
            child: _buildPromoList(),
          ),
          Obx(() {
            int totalPages = (controller.paging.value.pageSize -1);

            // Tính phạm vi hiển thị: hiển thị từ currentPage - 2 đến currentPage + 2, đảm bảo không vượt qua giới hạn 1 và totalPages.
            int startPage = controller.currentPage.value - 2 < 1 ? 1 : controller.currentPage.value - 2;
            int endPage = controller.currentPage.value + 2 > totalPages ? totalPages : controller.currentPage.value + 2;

            List<Widget> pageButtons = [];
            for (int i = startPage; i <= endPage; i++) {
              pageButtons.add(
                GestureDetector(
                  onTap: () => controller.goToPage(i),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    margin: EdgeInsets.symmetric(horizontal: 4),
                    decoration: BoxDecoration(
                      color:
                          (i == controller.currentPage.value+1) ? Colors.blue : Colors.grey[200],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      i.toString(),
                      style: TextStyle(
                        color: (i == controller.currentPage.value+1) ? Colors.white : Colors.black,
                        fontWeight: (i == controller.currentPage.value)
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                  ),
                ),
              );
            }
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: pageButtons,
            );
          })
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Quản lý khuyến mãi',
          style: style_S24_W600_BlackColor,
        ),
        ElevatedButton.icon(
          onPressed: () => _showAddPromoDialog(),
          icon: Icon(Icons.add, size: 20),
          label: Text('Thêm khuyến mãi'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: AppDimens.spaceMedium,
              vertical: AppDimens.spaceSmall,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilters() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(AppDimens.spaceMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => _buildBankFilterDropdown(
                      'Ngân hàng',
                      isNullOrEmpty(controller.selectedBankCode.value.bankCode)
                          ? (controller.banksData.isNotEmpty)
                              ? controller.banksData[0].bankCode!
                              : ''
                          : controller.selectedBankCode.value.bankCode!,
                      controller.banksData,
                      (value) => controller.selectedBankCode.value = value,
                    ),
                  ),
                ),
                SizedBox(width: AppDimens.spaceMedium),
                Expanded(
                  child: Obx(
                    () => _buildFilterDropdown(
                      'Danh mục',
                      controller.selectedCategory.value.isEmpty
                          ? 'Mua sắm'
                          : controller.selectedCategory.value,
                      PromoCategory.values.map((e) => e.displayName).toList(),
                      (value) => controller.selectedCategory.value = value,
                    ),
                  ),
                ),
                SizedBox(width: AppDimens.spaceMedium),
                Expanded(
                  child: _buildFilterDropdown(
                    'Trạng thái',
                    controller.selectedStatus.value
                        ? 'Hoạt động'
                        : 'Không hoạt động',
                    ['Hoạt động', 'Không hoạt động'],
                    (value) =>
                        controller.selectedStatus.value = value == 'Hoạt động',
                  ),
                ),
              ],
            ),
            SizedBox(height: AppDimens.spaceMedium),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: controller.resetFilters,
                  child: Text('Đặt lại'),
                  style: TextButton.styleFrom(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                SizedBox(width: AppDimens.spaceSmall),
                ElevatedButton(
                  onPressed: controller.applyFilters,
                  child: Text('Áp dụng'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankFilterDropdown(
    String label,
    String value,
    List<BankData> items,
    Function(BankData) onChanged,
  ) {
    BankData validValue = BankData(
      bankName: 'NH TMCP CONG THUONG VN',
      bankCode: 'ICB',
      shortName: 'VietinBank',
      icon:
          'https://cardpromo.s3.ap-southeast-2.amazonaws.com/bank_icon_1743470921317_icon_Vietinbank.svg',
    );
    bool isChooseValue = false;
    for (BankData bank in items) {
      if (bank.bankCode == value) {
        validValue = bank;
        isChooseValue = true;
        break;
      }
    }
    if (!isChooseValue && items.isNotEmpty) {
      validValue = items.first;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: style_S14_W400_GreyColor,
        ),
        SizedBox(height: AppDimens.spaceSmall),
        Container(
          padding: EdgeInsets.symmetric(horizontal: AppDimens.spaceSmall),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: DropdownButton<BankData>(
            value: validValue,
            isExpanded: true,
            underline: SizedBox(),
            items: items.map((item) {
              return DropdownMenuItem<BankData>(
                value: item,
                child: Text('(${item.bankCode}) - ${item.bankName}'),
              );
            }).toList(),
            onChanged: (value) => onChanged(value!),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    Function(String) onChanged,
  ) {
    final validValue = items.contains(value) ? value : items.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: style_S14_W400_GreyColor,
        ),
        SizedBox(height: AppDimens.spaceSmall),
        Container(
          padding: EdgeInsets.symmetric(horizontal: AppDimens.spaceSmall),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: DropdownButton<String>(
            value: validValue,
            isExpanded: true,
            underline: SizedBox(),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: (value) => onChanged(value!),
          ),
        ),
      ],
    );
  }

  Widget _buildPromoList() {
    return Obx(() {
      if (controller.filteredPromos.isEmpty) {
        return Center(
          child: Text(
            'Không có khuyến mãi nào',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        );
      }

      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Số lượng: ${controller.filteredPromos.length}'),
          Expanded(
            child: ListView.builder(
              itemCount: controller.filteredPromos.length,
              itemBuilder: (context, index) {
                final promo = controller.filteredPromos[index];
                return Card(
                  margin: EdgeInsets.only(bottom: AppDimens.spaceMedium),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: ListTile(
                    contentPadding: EdgeInsets.all(AppDimens.spaceMedium),
                    leading: CircleAvatar(
                      radius: 25,
                      child: promo.promoImageUrl == null
                          ? Icon(Icons.local_offer, size: 25)
                          : null,
                    ),
                    title: Text(
                      promo.title ?? '',
                      style: style_S16_W600_BlackColor,
                    ),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Ngân hàng: ${promo.bankName}'),
                        Text('Danh mục: ${promo.category?.join(", ")}'),
                        Text(
                            'Thời gian: ${promo.startDateAsDate} - ${promo.endDateAsDate}'),
                        Text('Merchant: ${promo.merchantName}'),
                      ],
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: Icon(Icons.edit, size: 20),
                          onPressed: () => _showEditPromoDialog(promo),
                          style: IconButton.styleFrom(
                            backgroundColor: AppColors.primary.withOpacity(0.1),
                            foregroundColor: AppColors.primary,
                          ),
                        ),
                        SizedBox(width: AppDimens.spaceSmall),
                        IconButton(
                          icon: Icon(Icons.delete, size: 20),
                          onPressed: () => _showDeletePromoDialog(promo.id!),
                          style: IconButton.styleFrom(
                            backgroundColor: AppColors.error.withOpacity(0.1),
                            foregroundColor: AppColors.error,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      );
    });
  }

  void _showAddPromoDialog() {
    final formKey = GlobalKey<FormState>();
    final titleController = TextEditingController();
    final descriptionController = TextEditingController();
    final budgetController = TextEditingController();
    final promoCodeController = TextEditingController();
    final linkPromoController = TextEditingController();
    final termsController = TextEditingController();
    final storeLocationController = TextEditingController();
    final promoImageUrlController = TextEditingController();
    final startDate = DateTime.now().obs;
    final endDate = DateTime.now().add(Duration(days: 30)).obs;

    final selectedMerchant = Merchant(
      id: '',
      name: '',
      email: '',
      phone: '',
      locations: [],
    ).obs;
    final searchMerchantController = TextEditingController();
    final active = true.obs;
    final sendNotification = true.obs;
    final schemes = <String>[].obs;
    final bins = <String>[].obs;
    controller.categoryCreatePromo.value.clear();
    controller.categoryCreatePromo.value
        .addAll(PromoCategory.values.map((e) => e.displayName));
    controller.categoryCreatePromo.refresh();
    Get.dialog(
      Dialog(
        child: Container(
          width: 1000,
          padding: EdgeInsets.all(AppDimens.spaceMedium),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Thêm khuyến mãi mới',
                        style: style_S20_W600_BlackColor,
                      ),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Get.back(),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceMedium),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Cột 1
                      Expanded(
                        child: Column(
                          children: [
                            _buildFormField(
                              'Tiêu đề',
                              titleController,
                              validator: (value) => value?.isEmpty ?? true
                                  ? 'Vui lòng nhập tiêu đề'
                                  : null,
                            ),
                            _buildFormField(
                              'Mô tả',
                              maxLines: 10,
                              descriptionController,
                              validator: (value) => value?.isEmpty ?? true
                                  ? 'Vui lòng nhập mô tả'
                                  : null,
                            ),
                            _buildFormField('Ngân sách', budgetController,
                                keyboardType: TextInputType.number),
                            _buildFormField(
                              'Mã khuyến mãi',
                              promoCodeController,
                            ),
                            _buildFormField(
                                'Link khuyến mãi', linkPromoController),
                            _buildFormField(
                              'URL hình ảnh',
                              promoImageUrlController,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceMedium),
                      // Cột 2
                      Expanded(
                        child: Column(
                          children: [
                            _buildFormField(
                              'Điều khoản và điều kiện',
                              termsController,
                              maxLines: 3,
                            ),
                            _buildFormField(
                              'Địa chỉ cửa hàng',
                              storeLocationController,
                            ),
                            _buildDatePicker(
                              'Ngày bắt đầu',
                              startDate,
                              (date) => startDate.value = date,
                            ),
                            _buildDatePicker(
                              'Ngày kết thúc',
                              endDate,
                              (date) => endDate.value = date,
                            ),
                            _buildMerchantSelector(
                              selectedMerchant,
                              searchMerchantController,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceMedium),
                  // Các trường khác
                  Row(
                    children: [
                      Expanded(
                        child: Obx(
                          () => _buildBankFilterDropdown(
                            'Ngân hàng',
                            isNullOrEmpty(
                                    controller.selectedBankCode.value.bankCode)
                                ? (controller.banksData.isNotEmpty)
                                    ? controller.banksData[0].bankCode!
                                    : ''
                                : controller.selectedBankCode.value.bankCode!,
                            controller.banksData,
                            (value) =>
                                controller.selectedBankCode.value = value,
                          ),
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceMedium),
                      Expanded(
                        child: _buildSelectCategory(),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceMedium),
                  // Scheme và Bins
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Scheme', style: style_S14_W400_GreyColor),
                            SizedBox(height: AppDimens.spaceSmall),
                            Obx(() => Wrap(
                                  spacing: AppDimens.spaceSmall,
                                  children: [
                                    FilterChip(
                                      label: Text('Visa'),
                                      selected: schemes.contains('Visa'),
                                      selectedColor:
                                          AppColors.primary.withOpacity(0.2),
                                      checkmarkColor: AppColors.primary,
                                      onSelected: (selected) {
                                        if (selected) {
                                          schemes.add('Visa');
                                        } else {
                                          schemes.remove('Visa');
                                        }
                                      },
                                    ),
                                    FilterChip(
                                      label: Text('Master'),
                                      selected: schemes.contains('Master'),
                                      selectedColor:
                                          AppColors.primary.withOpacity(0.2),
                                      checkmarkColor: AppColors.primary,
                                      onSelected: (selected) {
                                        if (selected) {
                                          schemes.add('Master');
                                        } else {
                                          schemes.remove('Master');
                                        }
                                      },
                                    ),
                                    FilterChip(
                                      label: Text('Napas'),
                                      selected: schemes.contains('Napas'),
                                      selectedColor:
                                          AppColors.primary.withOpacity(0.2),
                                      checkmarkColor: AppColors.primary,
                                      onSelected: (selected) {
                                        if (selected) {
                                          schemes.add('Napas');
                                        } else {
                                          schemes.remove('Napas');
                                        }
                                      },
                                    ),
                                  ],
                                )),
                          ],
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Bins', style: style_S14_W400_GreyColor),
                            SizedBox(height: AppDimens.spaceSmall),
                            TextField(
                              decoration: InputDecoration(
                                hintText: 'Nhập bins, cách nhau bằng dấu phẩy',
                                border: OutlineInputBorder(),
                              ),
                              onChanged: (value) {
                                bins.value = value
                                    .split(',')
                                    .map((e) => e.trim())
                                    .toList();
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceMedium),
                  // Các tùy chọn giảm giá và hoàn tiền
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Tùy chọn giảm giá',
                                style: style_S14_W400_GreyColor),
                            SizedBox(height: AppDimens.spaceSmall),
                            Obx(() => _buildPromoOptionAmountReduce(
                                controller.promoOptionAmountReduces)),
                          ],
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceMedium),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Tùy chọn hoàn tiền',
                                style: style_S14_W400_GreyColor),
                            SizedBox(height: AppDimens.spaceSmall),
                            Obx(() => _buildPromoOptionAmountRefund(
                                controller.promoOptionAmountRefund)),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceMedium),
                  // Các tùy chọn khác
                  Container(
                    width: double.infinity,
                    child: Row(
                      children: [
                        Expanded(
                          child: Obx(() => CheckboxListTile(
                                title: Text('Hoạt động'),
                                value: active.value,
                                onChanged: (value) =>
                                    active.value = value ?? false,
                                controlAffinity:
                                    ListTileControlAffinity.leading,
                              )),
                        ),
                        Expanded(
                          child: Obx(() => CheckboxListTile(
                                title: Text('Gửi thông báo'),
                                value: sendNotification.value,
                                onChanged: (value) =>
                                    sendNotification.value = value ?? false,
                                controlAffinity:
                                    ListTileControlAffinity.leading,
                              )),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: AppDimens.spaceMedium),
                  // Nút lưu
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Get.back(),
                        child: Text('Hủy'),
                      ),
                      SizedBox(width: AppDimens.spaceSmall),
                      ElevatedButton(
                        onPressed: () {
                          if (formKey.currentState?.validate() ?? false) {
                            PromoDetail promo = PromoDetail(
                                active: true, sendNotification: false);

                            if (!isNullOrEmpty(
                                controller.selectedBankCode.value.bankCode)) {
                              promo.bankCode =
                                  controller.selectedBankCode.value.bankCode;
                            } else if (controller.banksData.isNotEmpty) {
                              promo.bankCode =
                                  controller.banksData.first.bankCode;
                            }
                            if (!isNullOrEmpty(
                                controller.selectedBankCode.value.bankName)) {
                              promo.bankName =
                                  controller.selectedBankCode.value.bankName;
                            } else if (controller.banksData.isNotEmpty) {
                              promo.bankName =
                                  controller.banksData.first.bankName;
                            }
                            if (titleController.text.isNotEmpty) {
                              promo.title = titleController.text;
                            }
                            if (descriptionController.text.isNotEmpty) {
                              promo.description = descriptionController.text;
                            }
                            if (controller
                                .categoryCreatePromo.value.isNotEmpty) {
                              promo.category = [];
                              promo.category!
                                  .addAll(controller.categoryCreatePromo.value);
                            }
                            final parsedBudget =
                                int.tryParse(budgetController.text);
                            if (parsedBudget != null) {
                              promo.budget = parsedBudget;
                            }
                            if (startDate.value != null) {
                              promo.startDate =
                                  startDate.value.millisecondsSinceEpoch;
                            }
                            if (endDate.value != null) {
                              promo.endDate =
                                  endDate.value.millisecondsSinceEpoch;
                            }
                            if (promoCodeController.text.isNotEmpty) {
                              promo.promoCode = promoCodeController.text;
                            }
                            if (linkPromoController.text.isNotEmpty) {
                              promo.linkPromo = linkPromoController.text;
                            }
                            if (termsController.text.isNotEmpty) {
                              promo.termsAndConditions = termsController.text;
                            }
                            if (selectedMerchant.value.id != null) {
                              promo.merchantId = selectedMerchant.value.id;
                            }
                            if (selectedMerchant.value.name != null) {
                              promo.merchantName = selectedMerchant.value.name;
                            }
                            if (storeLocationController.text.isNotEmpty) {
                              promo.storeLocation =
                                  storeLocationController.text;
                            }
                            if (promoImageUrlController.text.isNotEmpty) {
                              promo.promoImageUrl =
                                  promoImageUrlController.text;
                            }
                            if (active.value != null) {
                              promo.active = active.value;
                            }
                            if (sendNotification.value != null) {
                              promo.sendNotification = sendNotification.value;
                            }
                            if (schemes.isNotEmpty) {
                              promo.scheme = schemes;
                            }
                            final parsedBins = bins
                                .map((e) => int.tryParse(e))
                                .whereType<int>()
                                .toList();
                            if (parsedBins.isNotEmpty) {
                              promo.bins = parsedBins;
                            }
                            if (controller
                                .promoOptionAmountReduces.isNotEmpty) {
                              promo.promoOptionAmountReduce =
                                  controller.promoOptionAmountReduces;
                            }
                            if (controller.promoOptionAmountRefund.isNotEmpty) {
                              promo.promoOptionAmountRefund =
                                  controller.promoOptionAmountRefund;
                            }

                            // final promo = PromoDetail(
                            //   title: titleController.text,
                            //   description: descriptionController.text,
                            //   budget: int.tryParse(budgetController.text),
                            //   startDate: startDate.value.millisecondsSinceEpoch,
                            //   endDate: endDate.value.millisecondsSinceEpoch,
                            //   promoCode: promoCodeController.text,
                            //   linkPromo: linkPromoController.text,
                            //   termsAndConditions: termsController.text,
                            //   merchantId: selectedMerchant.value.id,
                            //   merchantName: selectedMerchant.value.name,
                            //   storeLocation: storeLocationController.text,
                            //   promoImageUrl: promoImageUrlController.text,
                            //   active: active.value,
                            //   sendNotification: sendNotification.value,
                            //   scheme: schemes,
                            //   bins: bins.map((e) => int.tryParse(e) ?? 0).toList(),
                            //   promoOptionAmountReduce:  controller.promoOptionAmountReduces,
                            //   promoOptionAmountRefund:  controller.promoOptionAmountRefund,
                            // );
                            controller.addPromo(promo);
                          }
                        },
                        child: Text('Thêm'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormField(
    String label,
    TextEditingController controller, {
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimens.spaceMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: style_S14_W400_GreyColor,
          ),
          SizedBox(height: AppDimens.spaceSmall),
          TextFormField(
            controller: controller,
            maxLines: maxLines,
            keyboardType: keyboardType,
            validator: validator,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: EdgeInsets.all(AppDimens.spaceSmall),
              filled: true,
              fillColor: Colors.grey[50],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDatePicker(
    String label,
    Rx<DateTime> date,
    Function(DateTime) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: style_S14_W400_GreyColor,
        ),
        SizedBox(height: AppDimens.spaceSmall),
        Obx(() => InkWell(
              onTap: () async {
                final picked = await showDatePicker(
                  context: Get.context!,
                  initialDate: date.value,
                  firstDate: DateTime(2023),
                  lastDate: DateTime.now().add(Duration(days: 365)),
                );
                if (picked != null) {
                  onChanged(picked);
                }
              },
              child: Container(
                padding: EdgeInsets.all(AppDimens.spaceSmall),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.grey[50],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(DateFormat('dd/MM/yyyy').format(date.value)),
                    Icon(Icons.calendar_today, size: 20),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  Widget _buildMerchantSelector(
    Rx<Merchant> selectedMerchant,
    TextEditingController searchController,
  ) {
    final isExpanded = false.obs;
    final searchText = ''.obs;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Chọn merchant',
          style: style_S14_W400_GreyColor,
        ),
        SizedBox(height: AppDimens.spaceSmall),
        Obx(() {
          if (selectedMerchant.value.id.isEmpty) {
            return ElevatedButton.icon(
              onPressed: () => isExpanded.value = true,
              icon: Icon(Icons.store),
              label: Text('Chọn merchant'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: AppColors.white,
                minimumSize: Size(double.infinity, 48),
              ),
            );
          } else {
            return Card(
              child: Padding(
                padding: EdgeInsets.all(AppDimens.spaceMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              selectedMerchant.value.name,
                              style: style_S16_W600_BlackColor,
                            ),
                            SizedBox(height: AppDimens.spaceSmall),
                            Text(
                              selectedMerchant.value.email,
                              style: style_S14_W400_GreyColor,
                            ),
                          ],
                        ),
                        IconButton(
                          icon: Icon(Icons.edit),
                          onPressed: () => isExpanded.value = true,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }
        }),
        Obx(() => isExpanded.value
            ? Card(
                margin: EdgeInsets.only(top: AppDimens.spaceSmall),
                child: Padding(
                  padding: EdgeInsets.all(AppDimens.spaceMedium),
                  child: Column(
                    children: [
                      TextField(
                        controller: searchController,
                        decoration: InputDecoration(
                          hintText: 'Tìm kiếm merchant...',
                          prefixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(),
                          suffixIcon: IconButton(
                            icon: Icon(Icons.close),
                            onPressed: () => isExpanded.value = false,
                          ),
                        ),
                        onChanged: (value) {
                          searchText.value = value;
                          controller.searchMerchant(value);
                        },
                      ),
                      SizedBox(height: AppDimens.spaceMedium),
                      Container(
                        height: 200,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListView.builder(
                          itemCount: controller.filteredMerchants.length,
                          itemBuilder: (context, index) {
                            final merchant =
                                controller.filteredMerchants[index];
                            return ListTile(
                              leading: CircleAvatar(
                                child: Text(merchant.name[0].toUpperCase()),
                                backgroundColor: AppColors.primary,
                              ),
                              title: Text(merchant.name),
                              subtitle: Text(merchant.email),
                              selected:
                                  selectedMerchant.value.id == merchant.id,
                              onTap: () {
                                selectedMerchant.value = merchant;
                                isExpanded.value = false;
                              },
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : SizedBox.shrink()),
      ],
    );
  }

  Widget _buildPromoOptionAmountReduce(
      RxList<PromoOptionAmountReduce> options) {
    return Column(
      children: [
        ...options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;

          return Card(
            elevation: 2,
            margin: EdgeInsets.only(bottom: AppDimens.spaceSmall),
            child: Padding(
              padding: EdgeInsets.all(AppDimens.spaceSmall),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: CommonTextField(
                          controller: controller.minAmountControllers[index],
                          hintText: 'Số tiền tối thiểu',
                          keyboardType: TextInputType.number,
                          suffix: Text('₫', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountReduce(
                              minAmount: int.tryParse(value) ?? 0,
                              fixAmountReduce: option.fixAmountReduce,
                              rateAmountReduce: option.rateAmountReduce,
                              maxAmountReduce: option.maxAmountReduce,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceSmall),
                      Expanded(
                        child: CommonTextField(
                          controller: controller.fixAmountControllers[index],
                          hintText: 'Số tiền giảm cố định',
                          keyboardType: TextInputType.number,
                          suffix: Text('₫', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountReduce(
                              minAmount: option.minAmount,
                              fixAmountReduce: int.tryParse(value) ?? 0,
                              rateAmountReduce: option.rateAmountReduce,
                              maxAmountReduce: option.maxAmountReduce,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceSmall),
                  Row(
                    children: [
                      Expanded(
                        child: CommonTextField(
                          controller: controller.rateAmountControllers[index],
                          hintText: 'Tỷ lệ giảm',
                          keyboardType: TextInputType.number,
                          suffix: Text('%', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountReduce(
                              minAmount: option.minAmount,
                              fixAmountReduce: option.fixAmountReduce,
                              rateAmountReduce: int.tryParse(value) ?? 0,
                              maxAmountReduce: option.maxAmountReduce,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceSmall),
                      Expanded(
                        child: CommonTextField(
                          controller: controller.maxAmountControllers[index],
                          hintText: 'Số tiền giảm tối đa',
                          keyboardType: TextInputType.number,
                          suffix: Text('₫', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountReduce(
                              minAmount: option.minAmount,
                              fixAmountReduce: option.fixAmountReduce,
                              rateAmountReduce: option.rateAmountReduce,
                              maxAmountReduce: int.tryParse(value) ?? 0,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceSmall),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: Icon(Icons.delete, color: AppColors.error),
                        onPressed: () => removePromoOption(index),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }).toList(),
        ElevatedButton.icon(
          onPressed: () {
            PromoOptionAmountReduce option = PromoOptionAmountReduce(
              minAmount: 0,
              fixAmountReduce: 0,
              rateAmountReduce: 0,
              maxAmountReduce: 0,
            );
            options.add(option);
            controller.minAmountControllers
                .add(TextEditingController(text: option.minAmount.toString()));
            controller.fixAmountControllers.add(
                TextEditingController(text: option.fixAmountReduce.toString()));
            controller.rateAmountControllers.add(TextEditingController(
                text: option.rateAmountReduce.toString()));
            controller.maxAmountControllers.add(
                TextEditingController(text: option.maxAmountReduce.toString()));
          },
          icon: Icon(Icons.add),
          label: Text('Thêm tùy chọn'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
          ),
        ),
      ],
    );
  }

  void removePromoOption(int index) {
    controller.promoOptionAmountReduces.removeAt(index);
    controller.minAmountControllers.removeAt(index);
    controller.fixAmountControllers.removeAt(index);
    controller.rateAmountControllers.removeAt(index);
    controller.maxAmountControllers.removeAt(index);
  }

  Widget _buildPromoOptionAmountRefund(
      RxList<PromoOptionAmountRefund> options) {
    return Column(
      children: [
        ...options.asMap().entries.map((entry) {
          final index = entry.key;
          final option = entry.value;

          return Card(
            elevation: 2,
            margin: EdgeInsets.only(bottom: AppDimens.spaceSmall),
            child: Padding(
              padding: EdgeInsets.all(AppDimens.spaceSmall),
              child: Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: CommonTextField(
                          controller:
                              controller.minAmountRefundControllers[index],
                          hintText: 'Số tiền tối thiểu',
                          keyboardType: TextInputType.number,
                          suffix: Text('₫', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountRefund(
                              minAmount: int.tryParse(value) ?? 0,
                              fixAmountRefund: option.fixAmountRefund,
                              rateAmountRefund: option.rateAmountRefund,
                              maxAmountRefund: option.maxAmountRefund,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceSmall),
                      Expanded(
                        child: CommonTextField(
                          controller:
                              controller.fixAmountRefundControllers[index],
                          hintText: 'Số tiền hoàn cố định',
                          keyboardType: TextInputType.number,
                          suffix: Text('₫', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountRefund(
                              minAmount: option.minAmount,
                              fixAmountRefund: int.tryParse(value) ?? 0,
                              rateAmountRefund: option.rateAmountRefund,
                              maxAmountRefund: option.maxAmountRefund,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceSmall),
                  Row(
                    children: [
                      Expanded(
                        child: CommonTextField(
                          controller:
                              controller.rateAmountRefundControllers[index],
                          hintText: 'Tỷ lệ hoàn',
                          keyboardType: TextInputType.number,
                          suffix: Text('%', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountRefund(
                              minAmount: option.minAmount,
                              fixAmountRefund: option.fixAmountRefund,
                              rateAmountRefund: int.tryParse(value) ?? 0,
                              maxAmountRefund: option.maxAmountRefund,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                      SizedBox(width: AppDimens.spaceSmall),
                      Expanded(
                        child: CommonTextField(
                          controller:
                              controller.maxAmountRefundControllers[index],
                          hintText: 'Số tiền hoàn tối đa',
                          keyboardType: TextInputType.number,
                          suffix: Text('₫', style: TextStyle(fontSize: 16)),
                          hideUnderBorderLine: true,
                          onChanged: (value) {
                            final newOption = PromoOptionAmountRefund(
                              minAmount: option.minAmount,
                              fixAmountRefund: option.fixAmountRefund,
                              rateAmountRefund: option.rateAmountRefund,
                              maxAmountRefund: int.tryParse(value) ?? 0,
                            );
                            options[index] = newOption;
                          },
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: AppDimens.spaceSmall),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      IconButton(
                        icon: Icon(Icons.delete, color: AppColors.error),
                        onPressed: () => removePromoOptionRefund(index),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        }).toList(),
        ElevatedButton.icon(
          onPressed: () {
            PromoOptionAmountRefund option = PromoOptionAmountRefund(
              minAmount: 0,
              fixAmountRefund: 0,
              rateAmountRefund: 0,
              maxAmountRefund: 0,
            );
            options.add(option);
            controller.minAmountRefundControllers
                .add(TextEditingController(text: option.minAmount.toString()));
            controller.fixAmountRefundControllers.add(
                TextEditingController(text: option.fixAmountRefund.toString()));
            controller.rateAmountRefundControllers.add(TextEditingController(
                text: option.rateAmountRefund.toString()));
            controller.maxAmountRefundControllers.add(
                TextEditingController(text: option.maxAmountRefund.toString()));
          },
          icon: Icon(Icons.add),
          label: Text('Thêm tùy chọn'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: AppColors.white,
          ),
        ),
      ],
    );
  }

  void removePromoOptionRefund(int index) {
    controller.promoOptionAmountRefund.removeAt(index);
    controller.minAmountRefundControllers.removeAt(index);
    controller.fixAmountRefundControllers.removeAt(index);
    controller.rateAmountRefundControllers.removeAt(index);
    controller.maxAmountRefundControllers.removeAt(index);
  }

  void _showEditPromoDialog(PromoDetail promo) {
    // TODO: Implement edit promo dialog
  }

  void _showDeletePromoDialog(String id) {
    Get.dialog(
      AlertDialog(
        title: Text('Xóa khuyến mãi'),
        content: Text('Bạn có chắc chắn muốn xóa khuyến mãi này?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deletePromo(id);
              Get.back();
            },
            child: Text('Xóa'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectCategory() {
    return PopupMenuButton<String>(
      // Bọc phần hiển thị child (DropdownButton style)
      child: Obx(() {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey),
            borderRadius: BorderRadius.circular(5.0),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Text(
                  controller.categoryCreatePromo.join(', '),
                  style: const TextStyle(fontSize: 14.0),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Icon(Icons.arrow_drop_down),
            ],
          ),
        );
      }),
      itemBuilder: (context) {
        return PromoCategory.values.map((cat) {
          return PopupMenuItem<String>(
            enabled: false, // để không auto close khi chọn
            child: StatefulBuilder(
              builder: (context, setState) {
                return Obx(() {
                  final isSelected =
                      controller.categoryCreatePromo.contains(cat.displayName);
                  return CheckboxListTile(
                    dense: true,
                    contentPadding: EdgeInsets.zero,
                    title: Text(cat.displayName),
                    value: isSelected,
                    onChanged: (bool? value) {
                      if (value == true) {
                        if (!controller.categoryCreatePromo
                            .contains(cat.displayName)) {
                          controller.categoryCreatePromo.add(cat.displayName);
                        }
                      } else {
                        controller.categoryCreatePromo.remove(cat.displayName);
                      }
                    },
                  );
                });
              },
            ),
          );
        }).toList();
      },
      onSelected: (_) {}, // không cần xử lý vì checkbox đã xử lý xong
    );
  }
}
