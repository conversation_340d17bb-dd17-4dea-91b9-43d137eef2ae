import 'dart:convert';

import 'package:card_promo/data/provider/base_service.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../data/model/merchant.dart';
import '../res/color/app_colors.dart';
import '../util/api_constant.dart';
import '../util/app_dimens.dart';
import '../util/app_utils.dart';

class WidgetCreateMerchant extends StatefulWidget {
  // WidgetCreateMerchant({super.key, required merchants});
  WidgetCreateMerchant({required this.merchants});

  RxList<Merchant> merchants;

  @override
  State<WidgetCreateMerchant> createState() => _WidgetCreateMerchantState();
}

class _WidgetCreateMerchantState extends State<WidgetCreateMerchant> {

  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final locations = <MerchantLocation>[].obs;


  @override
  void initState() {
    // TODO: implement initState
  }


  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Row(
            children: [
              Flexible(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    TextField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: 'Tên merchant',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: AppDimens.spaceMedium),
                    TextField(
                      controller: emailController,
                      decoration: InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: AppDimens.spaceMedium),
                    TextField(
                      controller: phoneController,
                      decoration: InputDecoration(
                        labelText: 'Số điện thoại',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    SizedBox(height: AppDimens.spaceMedium),
                  ],
                ),
              ),
              SizedBox(width: 10,),
              Flexible(flex: 1,child: SingleChildScrollView(
                child: Column(
                  children: [
                    Text('Danh sách chi nhánh', style: TextStyle(fontWeight: FontWeight.bold)),
                    SizedBox(height: AppDimens.spaceMedium),
                    Obx(() => Column(
                      children: locations.map((location) => Card(
                        margin: EdgeInsets.only(bottom: AppDimens.spaceSmall),
                        child: ListTile(
                          title: Text(location.branchName ?? ''),
                          subtitle: Column(children: [
                            Text(location.address ?? ''),
                            Text('${location.location?.x ?? 0} - ${location.location?.y ?? 0}' )
                          ],),
                          trailing: IconButton(
                            icon: Icon(Icons.delete),
                            onPressed: () => locations.remove(location),
                          ),
                        ),
                      )).toList(),
                    )),
                    Divider(),
                    ElevatedButton.icon(
                      onPressed: () {
                        showAddLocation(locations);
                      },
                      icon: Icon(Icons.add_location),
                      label: Text('Thêm chi nhánh'),
                    ),
                  ],
                ),
              ),)
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextButton(
              onPressed: () => Get.back(),
              child: Text('Hủy'),
            ),
            SizedBox(width: 10,),
            ElevatedButton(
              onPressed: () async {
                if (nameController.text.isEmpty) {
                  Get.snackbar(
                    'Lỗi',
                    'Vui lòng điền đầy đủ thông tin',
                    snackPosition: SnackPosition.BOTTOM,
                  );
                  return;
                }

                final params = {
                  'name': nameController.text,
                  'email': emailController.text,
                  'phone': phoneController.text,
                  'locations': locations.isNotEmpty ? locations.map((loc) => loc.toJson()).toList() : [],
                };
                doAddMerchant(params);
                Get.back();
              },
              child: Text('Thêm'),
            ),
          ],
        )
      ],
    );
  }

  void doAddMerchant(var params) async {
    try {
      final result = await BaseService().postDataWeb(ApiConstant.urlAddMerchant, params);
      if (result['success']) {
        final data = result['data'];
        Merchant? merchant = AppUtils.parseResponse<Merchant>(
            context, jsonEncode(data), Merchant.fromJson);
        if (merchant != null) {
          widget.merchants.add(merchant);
          Get.snackbar(
            'Thành công',
            'Đã thêm merchant thành công',
            snackPosition: SnackPosition.BOTTOM,
          );
        }
      } else {
        Get.snackbar(
          'Lỗi',
          'Không thể thêm merchant: ${result['error']}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
      widget.merchants.refresh();
    } catch (e) {
      print('Error adding merchant: $e');
      Get.snackbar(
        'Lỗi',
        'Không thể thêm merchant',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void showAddLocation(RxList<MerchantLocation> locations) {
    final branchNameController = TextEditingController();
    final addressController = TextEditingController();
    final latController = TextEditingController();
    final longController = TextEditingController();

    Get.dialog(
      Dialog(
        child: Container(
          color: AppColors.white,
          padding: EdgeInsets.all(10),
          width: MediaQuery.of(context).size.width * 0.5, // 90% chiều rộng màn hình
          height: MediaQuery.of(context).size.height * 0.5,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Thêm chi nhánh'),
              SizedBox(height: 20,),
              TextField(
                controller: branchNameController,
                decoration: InputDecoration(
                  labelText: 'Tên chi nhánh',
                  border: OutlineInputBorder(),
                ),
              ),
              SizedBox(height: AppDimens.spaceMedium),
              TextField(
                controller: addressController,
                decoration: InputDecoration(
                  labelText: 'Địa chỉ',
                  border: OutlineInputBorder(),
                ),
              ),

              SizedBox(height: AppDimens.spaceMedium),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: latController,
                      decoration: InputDecoration(
                        labelText: 'Vĩ độ (lat) ',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                  SizedBox(width: AppDimens.spaceMedium),
                  Expanded(
                    child: TextField(
                      controller: longController,
                      decoration: InputDecoration(
                        labelText: 'Kinh độ (long)',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                    ),
                  ),
                ],
              ),

              Expanded(child: Container()),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: Text('Hủy'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      if (branchNameController.text.isEmpty) {
                        Get.snackbar(
                          'Lỗi',
                          'Vui lòng điền đầy đủ thông tin',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                        return;
                      }

                      MerchantLocation newMerchant = MerchantLocation();
                      if (latController.text.isNotEmpty && longController.text.isNotEmpty) {
                        final lat = double.tryParse(latController.text);
                        final long = double.tryParse(longController.text);

                        if (lat == null || long == null) {
                          Get.snackbar(
                            'Lỗi',
                            'Vĩ độ và kinh độ phải là số',
                            snackPosition: SnackPosition.BOTTOM,
                          );
                          return;
                        }

                        newMerchant.location = GeoJsonPoint(
                          x: lat,
                          y: long,
                          type: 'Point',
                          coordinates: [long, lat],
                        );
                      }

                      newMerchant.branchName = branchNameController.text;
                      newMerchant.address = addressController.text;

                      locations.add(newMerchant);
                      Get.back();
                    },
                    child: Text('Thêm'),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
