import 'dart:async';
import 'dart:convert';

import 'package:card_promo/res/color/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app_controller.dart';

import '../../data/provider/base_service.dart';
import '../../util/api_constant.dart';

class ContributeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ContributeController(), fenix: false);
  }
}

class ContributeController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  final TextEditingController promoController = TextEditingController();
  final TextEditingController contributorNameController = TextEditingController();
  RxInt countdown = 0.obs;

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void onReady() async {
    super.onReady();
  }

  void submitContribution() async {
    if (countdown.value > 0) return;

    String contribution = promoController.text.trim();
    if (contribution.isEmpty) {
      Get.snackbar(
        "Lỗi",
        "Vui lòng nhập thông tin khuyến mãi!",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white
      );
      return;
    }

    // Xác định tên người đóng góp
    String contributorName = "Người dùng đóng góp";
    if (_appController.isLoggedIn.value) {
      contributorName = _appController.user.value.userName ?? '';
    } else if (contributorNameController.text.trim().isNotEmpty) {
      contributorName = contributorNameController.text.trim();
    }

    Map params = {
      "description": contribution,
      "status": false,
      "userName": contributorName,
      "userId": _appController.user.value.userId,
    };

    _appController.showLoading();
    var response = await BaseService()
        .sendData(ApiConstant.urlContributionCreatePromo, jsonEncode(params));
    _appController.hideLoading();

    if (response.errorResponse.code == 200 && response.responseData.isNotEmpty) {
      var jsonMap = jsonDecode(response.responseData);
      if (jsonMap['result'] != null) {
        // Hiển thị thông báo cảm ơn
        Get.snackbar(
          "Cảm ơn bạn!",
          "Đóng góp của bạn đã được gửi để xét duyệt. Chúng tôi sẽ liên hệ lại sớm nhất!",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: Colors.white,
          duration: Duration(seconds: 5),
        );

        // Reset form
        promoController.clear();
        contributorNameController.clear();

        // Bắt đầu đếm ngược 30 giây
        countdown.value = 30;
        Timer.periodic(Duration(seconds: 1), (timer) {
          if (countdown.value > 0) {
            countdown.value--;
          } else {
            timer.cancel();
          }
        });
      }
    }
  }

  @override
  void dispose() {
    promoController.dispose();
    contributorNameController.dispose();
    super.dispose();
  }
}