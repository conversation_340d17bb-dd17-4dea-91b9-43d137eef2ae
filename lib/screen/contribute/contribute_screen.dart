import 'package:card_promo/res/string/app_strings.dart';
import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/screen/contribute/contribute_controller.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../util/styles.dart';

class ContributeScreen extends GetView<ContributeController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Scaffold(
        backgroundColor: AppColors.main_background,
        body: Stack(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              child: Container(child: SvgPicture.asset(AppImages.ic_header)),
            ),

            Column(
              children: [
                Container(
                  margin: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 10,
                      left: 10,
                      bottom: 10,
                      right: 10),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TouchableWidget(
                          padding: EdgeInsets.all(5),
                          onPressed: () {
                            Get.back();
                          },
                          child: Icon(
                            Icons.close,
                            color: AppColors.white,
                          )),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: Text(
                          AppStrings.getString(AppStrings.tt_contribute),
                          style: style_S18_W600_WhiteColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20)),
                        color: AppColors.background_white),
                    child: SingleChildScrollView(
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Phần giới thiệu
                            Text(
                              AppStrings.getString(AppStrings.sharePromoTitle),
                              style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              "Bạn đang có thông tin về khuyến mãi thẻ tín dụng? Hãy chia sẻ để cộng đồng cùng hưởng lợi!",
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                            const SizedBox(height: 10),

                            // Phần ví dụ
                            Container(
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade100,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Ví dụ đóng góp:",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: AppColors.primary,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  _buildExampleItem("Thanh toán thẻ Visa tại Aeon được giảm 100k"),
                                  _buildExampleItem("Mở thẻ Shinhan được tặng 500k"),
                                  _buildExampleItem("Link khuyến mãi: https://example.com/promo"),
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),

                            // Phần thông tin người đóng góp
                            Obx(() => _appController.isLoggedIn.value
                              ? Container(
                                  padding: EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.green.shade50,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.green.shade200),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.person, color: Colors.green),
                                      SizedBox(width: 8),
                                      Text(
                                        "Đóng góp bởi: ${_appController.user.value.userName}",
                                        style: TextStyle(
                                          color: Colors.green.shade700,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ],
                                  ),
                                )
                              : TextField(
                                  controller: controller.contributorNameController,
                                  decoration: InputDecoration(
                                    labelText: "Tên người đóng góp (không bắt buộc)",
                                    hintText: "Người dùng đóng góp",
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    prefixIcon: Icon(Icons.person_outline),
                                  ),
                                )),
                            const SizedBox(height: 10),

                            // Ô nhập liệu
                            TextField(
                              controller: controller.promoController,
                              maxLines: 3,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8)),
                                hintText: "Nhập thông tin khuyến mãi hoặc dán link...",
                              ),
                            ),
                            const SizedBox(height: 10),

                            // Lợi ích khi đóng góp
                            Container(
                              padding: EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.blue.shade200),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(Icons.star, color: Colors.blue),
                                      SizedBox(width: 8),
                                      Text(
                                        "Lợi ích khi đóng góp",
                                        style: TextStyle(
                                          color: Colors.blue.shade700,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 10),
                                  _buildBenefitItem("3 đóng góp hợp lệ → 1 tháng sử dụng không quảng cáo"),
                                  _buildBenefitItem("Vinh danh người đóng góp trên khuyến mãi"),
                                  _buildBenefitItem("Nhận ưu đãi đặc biệt từ các đối tác khi đóng góp nhiều"),
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),

                            // Nút gửi

                            Obx(
                              () => TouchableWidget(
                                  decoration: BoxDecoration(
                                    color: AppColors.main_background,
                                    borderRadius: BorderRadius.all(Radius.circular(10))
                                  ),
                                  onPressed: () {
                                if (controller.countdown.value == 0)
                                  controller.submitContribution();
                              }, child: Text(
                                controller.countdown.value > 0
                                    ? "Vui lòng đợi ${controller.countdown.value}s..."
                                    : "Gửi xem xét",
                                style: style_S16_W600_WhiteColor,
                              )),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildExampleItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.arrow_right, size: 20, color: Colors.grey.shade600),
          SizedBox(width: 8),
          Expanded(child: Text(text, style: TextStyle(fontSize: 14))),
        ],
      ),
    );
  }

  Widget _buildBenefitItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.check_circle, size: 20, color: Colors.blue),
          SizedBox(width: 8),
          Expanded(child: Text(text, style: TextStyle(fontSize: 14))),
        ],
      ),
    );
  }
}
