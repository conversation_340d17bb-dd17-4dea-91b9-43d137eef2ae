import 'package:card_promo/util/styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../data/model/merchant.dart';
import '../../util/local_storage.dart';
import '../../widget/itemPromoWidget.dart';
import 'merchant_promo_controller.dart';

class MerchantPromoScreen extends GetView<MerchantPromoController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        backgroundColor: AppColors.white,
        elevation: 0,
        title: Text(
          'K<PERSON>yến mãi theo Merchant',
          style: style_S20_W600_BlackColor,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: AppColors.blackText),
          onPressed: () => Get.back(),
        ),
      ),
      body: Obx(() {
        if (controller.selectedMerchant.value != null) {
          return _buildMerchantPromoView();
        } else {
          return _buildSearchView();
        }
      }),
    );
  }

  Widget _buildSearchView() {
    return Container(
      color: AppColors.white,
      child: Column(
        children: [
          // Search Bar - Material Design 3
          Container(
            margin: EdgeInsets.fromLTRB(20, 16, 20, 8),
            child: SearchBar(
              controller: controller.searchController,
              hintText: 'Tìm kiếm merchant...',
              hintStyle: WidgetStateProperty.all(
                style_S16_W400_BlackColor.copyWith(
                  color: AppColors.greyTextContent,
                ),
              ),
              textStyle: WidgetStateProperty.all(style_S16_W400_BlackColor),
              backgroundColor: WidgetStateProperty.all(AppColors.grayBackground),
              elevation: WidgetStateProperty.all(0),
              side: WidgetStateProperty.all(
                BorderSide(color: AppColors.lightGreyText, width: 1),
              ),
              shape: WidgetStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(28),
                ),
              ),
              leading: Icon(
                Icons.search_rounded,
                color: AppColors.main_background,
                size: 24,
              ),
              trailing: [
                Obx(() => controller.isSearching.value
                    ? Container(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(AppColors.main_background),
                        ),
                      )
                    : controller.searchController.text.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear_rounded, color: AppColors.greyTextContent),
                            onPressed: () {
                              controller.searchController.clear();
                              controller.searchResults.clear();
                              controller.isSearching.value = false;
                            },
                          )
                        : SizedBox.shrink()),
              ],
              onChanged: controller.onSearchChanged,
              onSubmitted: controller.onSearchSubmitted,
            ),
          ),

          // Search Results or Recent Merchants
          Expanded(
            child: Obx(() {
              if (controller.searchResults.isNotEmpty) {
                return _buildSearchResults();
              } else if (controller.recentMerchants.isNotEmpty && controller.searchController.text.isEmpty) {
                return _buildRecentMerchants();
              } else {
                return _buildEmptyState();
              }
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Container(
      color: AppColors.white,
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        itemCount: controller.searchResults.length,
        itemBuilder: (context, index) {
          final merchant = controller.searchResults[index];
          return Container(
            margin: EdgeInsets.only(bottom: 12),
            child: Material(
              elevation: 1,
              borderRadius: BorderRadius.circular(16),
              color: AppColors.white,
              child: InkWell(
                borderRadius: BorderRadius.circular(16),
                onTap: () => controller.selectMerchant(merchant),
                child: Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: AppColors.grayBorder, width: 1),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: AppColors.main_background.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          Icons.store_rounded,
                          color: AppColors.main_background,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              merchant.name,
                              style: style_S16_W600_BlackColor,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 4),
                            Text(
                              merchant.email.isNotEmpty ? merchant.email : 'Không có thông tin',
                              style: style_S14_W400_GreyColor,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 16,
                        color: AppColors.greyTextContent,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRecentMerchants() {
    return Container(
      color: AppColors.white,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tìm kiếm gần đây',
                  style: style_S18_W600_BlackColor,
                ),
                TextButton.icon(
                  onPressed: () async {
                    await LocalStorage().clearRecentMerchants();
                    await controller.loadRecentMerchants();
                  },
                  icon: Icon(
                    Icons.clear_all_rounded,
                    size: 16,
                    color: AppColors.blueText,
                  ),
                  label: Text(
                    'Xóa tất cả',
                    style: style_S14_W400_BlueColor,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: controller.recentMerchants.map((merchant) {
                return _buildRecentMerchantChip(merchant);
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentMerchantChip(Map<String, String> merchant) {
    return Material(
      elevation: 0,
      borderRadius: BorderRadius.circular(24),
      color: AppColors.main_background.withOpacity(0.1),
      child: InkWell(
        onTap: () => controller.selectRecentMerchant(merchant),
        borderRadius: BorderRadius.circular(24),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: AppColors.main_background.withOpacity(0.3), width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppColors.main_background,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.store_rounded,
                  size: 12,
                  color: AppColors.white,
                ),
              ),
              SizedBox(width: 8),
              Flexible(
                child: Text(
                  merchant['name'] ?? '',
                  style: style_S14_W600_MainColor,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      color: AppColors.white,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.main_background.withOpacity(0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                Icons.search_rounded,
                size: 60,
                color: AppColors.main_background,
              ),
            ),
            SizedBox(height: 24),
            Text(
              'Tìm kiếm merchant',
              style: style_S20_W600_BlackColor,
            ),
            SizedBox(height: 12),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                'Nhập tên merchant để tìm kiếm và xem danh sách khuyến mãi có sẵn',
                textAlign: TextAlign.center,
                style: style_S16_W400_BlackColor.copyWith(
                  color: AppColors.greyTextContent,
                ),
              ),
            ),
            SizedBox(height: 32),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.main_background.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppColors.main_background.withOpacity(0.3)),
              ),
              child: Text(
                '💡 Thử tìm: Circle K, Starbucks, KFC...',
                style: style_S14_W400_BlackColor.copyWith(
                  color: AppColors.main_background,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMerchantPromoView() {
    return Column(
      children: [
        // Merchant Header
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.white,
            boxShadow: [
              BoxShadow(
                color: AppColors.lightGreyText.withOpacity(0.1),
                blurRadius: 4,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  color: AppColors.main_background.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  Icons.store_rounded,
                  color: AppColors.main_background,
                  size: 28,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      controller.selectedMerchant.value?.name ?? '',
                      style: style_S18_W600_BlackColor,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.local_offer_rounded,
                          size: 16,
                          color: AppColors.main_background,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Danh sách khuyến mãi',
                          style: style_S14_W400_BlackColor.copyWith(
                            color: AppColors.main_background,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Material(
                color: AppColors.lightGreyText.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                child: InkWell(
                  onTap: controller.clearSelection,
                  borderRadius: BorderRadius.circular(20),
                  child: Container(
                    width: 40,
                    height: 40,
                    child: Icon(
                      Icons.close_rounded,
                      color: AppColors.greyTextContent,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Promo List
        Expanded(
          child: Obx(() {
            if (controller.isLoadingPromos.value) {
              return Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.main_background),
                ),
              );
            } else if (controller.merchantPromos.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.local_offer_outlined,
                      size: 64,
                      color: AppColors.lightGreyText,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Không có khuyến mãi',
                      style: style_S18_W600_BlackColor,
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Merchant này hiện chưa có\nchương trình khuyến mãi nào',
                      textAlign: TextAlign.center,
                      style: style_S14_W400_GreyColor,
                    ),
                  ],
                ),
              );
            } else {
              return SingleChildScrollView(
                padding: EdgeInsets.all(16),
                child: ItemPromoWidget(
                  lsPromoShow: controller.merchantPromos,
                ),
              );
            }
          }),
        ),
      ],
    );
  }
}
