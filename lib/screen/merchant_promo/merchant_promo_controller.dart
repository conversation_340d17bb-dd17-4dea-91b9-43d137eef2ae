import 'dart:convert';

import 'package:card_promo/util/api_constant.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/base_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../app_controller.dart';
import '../../data/model/login_model.dart';
import '../../data/model/user.dart';
import '../../data/provider/base_service.dart';
import '../../util/app_utils.dart';

class MerchantPromoBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> MerchantPromoController(), fenix: false);
  }
}

class MerchantPromoController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();

  }
}