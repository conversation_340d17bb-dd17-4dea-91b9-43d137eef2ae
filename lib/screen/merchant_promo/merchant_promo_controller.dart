import 'dart:convert';

import 'package:card_promo/util/api_constant.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/base_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../app_controller.dart';
import '../../data/model/login_model.dart';
import '../../data/model/user.dart';
import '../../data/model/merchant.dart';
import '../../data/model/promo_detail.dart';
import '../../data/provider/base_service.dart';
import '../../data/demo/demo_data.dart';
import '../../util/app_utils.dart';
import '../../util/local_storage.dart';

class MerchantPromoBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> MerchantPromoController(), fenix: false);
  }
}

class MerchantPromoController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();
  final LocalStorage _localStorage = LocalStorage();

  late BuildContext context;

  // Search related
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  // Observable variables
  var isLoading = false.obs;
  var isSearching = false.obs;
  var searchResults = <Merchant>[].obs;
  var recentMerchants = <Map<String, String>>[].obs;
  var selectedMerchant = Rxn<Merchant>();
  var merchantPromos = <PromoDetail>[].obs;
  var isLoadingPromos = false.obs;

  @override
  void onInit() async {
    super.onInit();
    await loadRecentMerchants();
  }

  @override
  void onReady() async {
    super.onReady();
  }

  @override
  void onClose() {
    searchController.dispose();
    searchFocusNode.dispose();
    super.onClose();
  }

  // Load recent merchants from local storage
  Future<void> loadRecentMerchants() async {
    try {
      final recent = await _localStorage.getRecentMerchants();
      recentMerchants.value = recent;
    } catch (e) {
      print('Error loading recent merchants: $e');
    }
  }

  // Search merchants - sử dụng demo data
  Future<void> searchMerchants(String query) async {
    if (query.trim().isEmpty) {
      searchResults.clear();
      isSearching.value = false;
      return;
    }

    try {
      isSearching.value = true;

      // Giả lập delay API call
      await Future.delayed(Duration(milliseconds: 500));

      // Sử dụng demo data
      final results = DemoData.searchMerchants(query.trim());
      searchResults.value = results;

      print('Found ${results.length} merchants for query: $query');
    } catch (e) {
      print('Error searching merchants: $e');
      searchResults.clear();
    } finally {
      isSearching.value = false;
    }
  }

  // Select merchant and load promos
  Future<void> selectMerchant(Merchant merchant) async {
    selectedMerchant.value = merchant;

    // Save to recent merchants
    await _localStorage.saveRecentMerchant(
      merchant.id,
      merchant.name,
      null, // No icon URL in current merchant model
    );

    // Reload recent merchants
    await loadRecentMerchants();

    // Clear search
    searchController.clear();
    searchResults.clear();
    isSearching.value = false;

    // Load promos for this merchant
    await loadMerchantPromos(merchant.id);
  }

  // Select merchant from recent
  Future<void> selectRecentMerchant(Map<String, String> recentMerchant) async {
    final merchantId = recentMerchant['id'] ?? '';
    final merchantName = recentMerchant['name'] ?? '';

    if (merchantId.isNotEmpty) {
      // Create a simple merchant object for display
      final merchant = Merchant(
        id: merchantId,
        name: merchantName,
        email: '',
        phone: '',
        locations: [],
      );

      selectedMerchant.value = merchant;
      await loadMerchantPromos(merchantId);
    }
  }

  // Load promos for selected merchant - sử dụng demo data
  Future<void> loadMerchantPromos(String merchantId) async {
    try {
      isLoadingPromos.value = true;
      merchantPromos.clear();

      // Giả lập delay API call
      await Future.delayed(Duration(milliseconds: 800));

      // Sử dụng demo data
      final promos = DemoData.getPromosByMerchantId(merchantId);
      merchantPromos.value = promos;

      print('Loaded ${promos.length} promos for merchant: $merchantId');
    } catch (e) {
      print('Error loading merchant promos: $e');
    } finally {
      isLoadingPromos.value = false;
    }
  }

  // Clear selection and go back to search
  void clearSelection() {
    selectedMerchant.value = null;
    merchantPromos.clear();
    searchController.clear();
    searchResults.clear();
    isSearching.value = false;
  }

  // Handle search submission
  void onSearchSubmitted(String query) {
    if (query.trim().isNotEmpty) {
      searchMerchants(query.trim());
    }
  }

  // Handle search text change
  void onSearchChanged(String query) {
    if (query.trim().isEmpty) {
      searchResults.clear();
      isSearching.value = false;
    }
  }
}