import 'dart:convert';

import 'package:card_promo/data/model/McCategory.dart';
import 'package:card_promo/data/model/card_data.dart';
import 'package:card_promo/data/model/config.dart';
import 'package:card_promo/res/image/app_images.dart';
import 'package:card_promo/util/ads_manager.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/crypto_helper.dart';
import 'package:card_promo/util/local_storage.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_swipe/flutter_swipe.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:card_promo/data/provider/base_service.dart';

import '../../app_controller.dart';
import '../../data/model/bank_data.dart';
import '../../data/model/banner.dart';
import '../../data/model/login_model.dart';
import '../../data/model/paging.dart';
import '../../data/model/promo_all_config.dart';
import '../../data/model/promo_data.dart';
import '../../data/model/promo_detail.dart';
import '../../data/model/promo_response.dart';
import '../../data/model/responseData.dart';
import '../../res/color/app_colors.dart';
import '../../res/string/app_strings.dart';
import '../../util/RSAEncryption.dart';
import '../../util/api_constant.dart';
import '../../util/app_utils.dart';
import '../../widget/add_card_widget.dart';
import '../../widget/base_dialog.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => HomeController(), fenix: false);
  }
}

class HomeController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  // late AnimationController aniController;
  // late Animation<double> animation;

  final SwiperController swpController = SwiperController();
  RxList<PromoDetail> lsPromoShow = <PromoDetail>[].obs;

  RxInt selectedIndexCategory = 0.obs;
  RxInt selectedIndexCard = 0.obs;

  RxList<CardData> listBankSaved = <CardData>[CardData(name: AppStrings.getString(AppStrings.tt_all))].obs;

  RxList listCategory = [AppStrings.getString(AppStrings.tt_suggest),].obs;

  RxInt pageIndex = 0.obs;
  Rx<Paging> paging = Paging(pageIndex: 0, pageSize: 1, dataCount: 0).obs;

  final baseService = BaseService();

  final ScrollController scrollController = ScrollController();
  final RxBool isLoading = false.obs;
  final RxBool hasMoreData = true.obs;

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
    initAndShowAds();
    scrollController.addListener(_onScroll);
  }

  Future<void> _loadLocalData() async {
    print('=== START _loadLocalData ===');
    selectedIndexCard.value = await LocalStorage().getData(LocalStorage.KEY_INDEX_CARD, 0);
    print('Index card seclect: ${selectedIndexCard.value}');

    List<CardData> dataSaved = await LocalStorage().getAllBankCard();
    print('Data Card saved lenght: ${dataSaved.length}');
    listBankSaved.value.addAll(dataSaved);
    listBankSaved.refresh();
    print('=== END _loadLocalData ===');
  }

  @override
  void onClose() {
    // TODO: implement onClose
    super.onClose();
    scrollController.dispose();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();
    // Load dữ liệu từ LocalStorage trước
    await _loadLocalData();
    // Đảm bảo dữ liệu LocalStorage đã được load xong trước khi gọi API
    await _initializeApp();
  }

  Future<void> _initializeApp() async {
    String AESKey = await LocalStorage().getData(LocalStorage.KEY_UFO, '');
    if (AESKey.isEmpty) {
      ResponseData response = await baseService.getData(ApiConstant.urlUfo);
      if (response.responseData.isNotEmpty) {
        print('${response.responseData}');
        var data = jsonDecode(response.responseData);
        if (data['ufo'] != null) {
          String clearKey = RSAEncryption.decryptData(data['ufo']);
          print('clearKey: $clearKey');
          await LocalStorage().saveData(LocalStorage.KEY_UFO, clearKey);
          await initConfig();
        }
      }
    } else {
      await initConfig();
    }
  }

  void _onScroll() {
    if (scrollController.position.pixels == scrollController.position.maxScrollExtent) {
      if (!isLoading.value && hasMoreData.value) {
        loadMoreData();
      }
    }
  }

  void loadMoreData() {
    print('load more data');
    if (pageIndex.value < (paging.value.pageSize - 1)) {
      pageIndex.value++;
      fetchData();
    } else {
      hasMoreData.value = false;
    }
  }

  bool isShowCard() {
    return (_appController.loginModel != null &&
        _appController.loginModel!.cardDetails.isNotEmpty);
  }

  void selectCategory(int index) {
    selectedIndexCategory.value = index;
    listCategory.refresh();
    pageIndex.value = 0;
    fetchData();
  }

  void selectCard(int index) async {
    selectedIndexCard.value = index;
    await LocalStorage().saveData(LocalStorage.KEY_INDEX_CARD, index);
    listBankSaved.refresh();
    pageIndex.value = 0;
    fetchData();
  }

  void onPressDetailPromo(PromoDetail promoData) async {

    if (promoData != null) {
      Get.toNamed(
        AppNameRoute.promo_detai_screen,
        arguments: jsonEncode(promoData),
      );
    }
    return;
    /*
    Map params = {'promoId': id};
    _appController.showLoading();
    var response = await BaseService()
        .sendData(ApiConstant.urlGetDetailPromo, jsonEncode(params));
    _appController.hideLoading();
    if (response.errorResponse.code == 200 &&
        response.responseData.isNotEmpty) {
      PromoDetail? promoData = AppUtils.parseResponse<PromoDetail>(
          context, response.responseData, PromoDetail.fromJson);
      if (promoData != null) {
        Get.toNamed(
          AppNameRoute.promo_detai_screen,
          arguments: jsonEncode(promoData),
        );
      }
    } else {
      AppUtils.showDialogAlert(
        context,
        type: BaseDialog.TYPE_ERROR,
        title: 'Có lỗi xảy ra, Vui lòng kiểm tra kết nối mạng và thử lại.',
        description: '${response.errorResponse.message}',
      );
    }
     */
  }

  Future<void> initConfig() async {
    print('=== START initConfig ===');
    // Khởi tạo categories trước
    for (var category in PromoCategory.values) {
      listCategory.add(category.displayName);
    }
    listCategory.refresh();

    // Gọi fetchAllConfig trước để load config
    await fetchAllConfig();

    // Sau đó mới gọi fetchData với dữ liệu đã sẵn sàng
    await fetchData();
    print('=== END initConfig ===');
  }

  Future<void> fetchData() async {
    if (isLoading.value) {
      return;
    }
    print('=== START fetchData ===');
    print('listBankSaved.length: ${listBankSaved.length}');
    print('selectedIndexCard.value: ${selectedIndexCard.value}');

    if (pageIndex.value == 0) {
      lsPromoShow.value.clear();
      lsPromoShow.refresh();
    }

    String bankCode = '';
    String scheme = '';
    String category = '';
    String pan = '';
    if ((selectedIndexCategory.value != 0) && (listCategory.length >= selectedIndexCategory.value)) {
      category = listCategory[selectedIndexCategory.value];
    }
    if ((selectedIndexCard.value >= 1) && (listBankSaved.length >= selectedIndexCard.value)) {
      bankCode = listBankSaved[selectedIndexCard.value].name ?? '';
      scheme = listBankSaved[selectedIndexCard.value].scheme ?? '';
      pan = listBankSaved[selectedIndexCard.value].pan ?? '';
      print('Selected card: bankCode=$bankCode, scheme=$scheme, pan=$pan');
    } else {
      print('No card selected or index out of range');
    }

    Map params = {
      'bankCode': bankCode,
      'category': category,
      'scheme': scheme,
      'pan': pan,
      "active": true,
      "pageIndex": pageIndex.value,
    };
    showLoadMore();

    var response = await baseService
        .sendData(ApiConstant.urlGetPromo, jsonEncode(params));
    hideLoadMore();
    try {
      if (response.errorResponse.code == 200 &&
          response.responseData.isNotEmpty) {
        PromoResponse? promoData = AppUtils.parseResponse<PromoResponse>(context, response.responseData, PromoResponse.fromJson);
        if (promoData != null) {
          lsPromoShow.value.addAll(promoData.promoData);
          lsPromoShow.refresh();
          paging.value = promoData.paging;
          hasMoreData.value = pageIndex.value < (paging.value.pageSize - 1);
        }
      } else {
        AppUtils.showDialogAlert(
          context,
          type: BaseDialog.TYPE_ERROR,
          title: 'Có lỗi xảy ra, Vui lòng kiểm tra kết nối mạng và thử lại.',
          description: '${response.errorResponse.message}',
        );
      }
    }catch (e) {
      print('Error fetching data: $e');
    }finally {
      hideLoadMore();
    }
  }

  void onPressBanner(String url) {
    Get.toNamed(AppNameRoute.webview_screen, arguments: url);
  }

  void addCardBank() {
    showModalBottomSheet(
        context: context,
        builder: (context) {
          return Obx(() => AddCardWidget(listBankSaved: listBankSaved.value, listBankOriginal: _appController.listBank.value, onRefresh: () {
            listBankSaved.refresh();
          },));
        });
  }

  void fetchBanner() async {
    _appController.showLoading();
    var response = await baseService.getData(ApiConstant.urlBanner);
    _appController.hideLoading();
    if (response.errorResponse.code == 200 &&
        response.responseData.isNotEmpty) {
      try {
        var map = jsonDecode(response.responseData);
        if (map['result'] != null) {
          List<BannerData> promoData = (map['result'] as List<dynamic>)
              .map((item) => BannerData.fromJson(item as Map<String, dynamic>))
              .toList();
          if (promoData.isNotEmpty) {
            if (_appController.listBanner.value.isEmpty) {
              _appController.listBanner.value.addAll(promoData);
              _appController.listBanner.refresh();
            }
            await LocalStorage().saveData(LocalStorage.KEY_BANNER, jsonEncode(promoData));
          }
        }
      } catch (e) {
        e.printError();
      }
    }
  }

  void fetchBankData() async {
    _appController.showLoading();
    var response = await baseService.getData(ApiConstant.urlAllBank);
    _appController.hideLoading();
    if (response.errorResponse.code == 200 &&
        response.responseData.isNotEmpty) {
      try {
        var map = jsonDecode(response.responseData);
        if (map['result'] != null) {
          List<BankData> banks = (map['result'] as List<dynamic>)
              .map((item) => BankData.fromJson(item as Map<String, dynamic>))
              .toList();
          if (banks.isNotEmpty) {
            banks.removeAt(0);
            _appController.listBank.value.addAll(banks);
            _appController.listBank.refresh();
            await LocalStorage().saveData(LocalStorage.KEY_BANK_CONFIG, jsonEncode(banks));
          }
        }
      } catch (e) {
        e.printError();
      }
    }
  }

  Future<void> fetchAllConfig() async {
    try {
      var response = await baseService.getData(ApiConstant.urlAllConfig);
      if (response.errorResponse.code == 200 &&
          response.responseData.isNotEmpty) {
        var map = jsonDecode(response.responseData);
        if (map['result'] != null) {
          _appController.config.value = Config.fromJson(map['result']);
          _appController.listBank.value = _appController.config.value.banks ?? [];
          _appController.listBank.value.removeAt(0);
          _appController.listBank.refresh();
          cacheData();

          checkReloadBanner();
        }
      }
    }catch (e) {
      e.printError();
      checkReloadBanner();
      // checkReloadBanks();
      loadCacheConfig();
    }
  }

  checkReloadBanner() async {
    if (_appController.config.value.isReloadBanner == '1') {
      fetchBanner();
      return;
    }
    String bannerData = await LocalStorage().getData(LocalStorage.KEY_BANNER, '');
    if (bannerData.isNotEmpty) {
      if (_appController.listBanner.value.isEmpty) {
        _appController.listBanner.value.addAll(List<BannerData>.from(jsonDecode(bannerData).map((x) => BannerData.fromJson(x))));
        // _appController.listBanner.value.addAll(jsonDecode(bannerData));
        _appController.listBanner.refresh();
      }
    }else {
      fetchBanner();
    }
  }

  void initAndShowAds() async {
    // mở app 3 lần mới hiện.
    int countOpenApp = await LocalStorage().getData(LocalStorage.KEY_OPEN_APP, 0);
    int pointContribute = await LocalStorage().getData(LocalStorage.KEY_CONTRIBUTION_POINT, 0);
    print('count open app: ${countOpenApp}');
    print('pointContribute: ${pointContribute}');
    if (countOpenApp > 3 && pointContribute == 0) {
      AdsManager().createAdsOpenApp(() {
        AdsManager().showAdsOpenApp();
      });
    } else {
      await LocalStorage().saveData(LocalStorage.KEY_OPEN_APP, countOpenApp+1);
    }
  }

  void cacheData() async {
    if ((_appController.config.value.banks != null) && _appController.config.value.banks!.isNotEmpty) {
      // _appController.listBank.value.addAll(_appController.config.value.banks ?? []);
      // _appController.listBank.refresh();
      await LocalStorage().saveData(LocalStorage.KEY_BANK_CONFIG, jsonEncode(_appController.config.value.banks));
    }

    if ((_appController.config.value.merchantsCategory != null) && _appController.config.value.merchantsCategory!.isNotEmpty) {
      await LocalStorage().saveData(LocalStorage.KEY_MC_CATEGORY_CONFIG, jsonEncode(_appController.config.value.merchantsCategory));
    }
  }

  void loadCacheConfig() async {
    String banks = await LocalStorage().getData(LocalStorage.KEY_BANK_CONFIG, '');
    if (banks.isNotEmpty) {
      _appController.listBank.value.addAll(List<BankData>.from(jsonDecode(banks).map((x) => BankData.fromJson(x))));
      _appController.listBank.value.removeAt(0);
      _appController.listBank.refresh();
    }else {
      fetchBankData();
    }

    String merchantCategories = await LocalStorage().getData(LocalStorage.KEY_MC_CATEGORY_CONFIG, '');
    if (banks.isNotEmpty) {
      _appController.config.value.merchantsCategory = [];
      _appController.config.value.merchantsCategory!.addAll(List<McCategory>.from(jsonDecode(merchantCategories).map((x) => McCategory.fromJson(x))));
      _appController.config.refresh();
    }
  }

  showLoadMore() {
    isLoading.value = true;
  }

  hideLoadMore() {
    isLoading.value = false;
  }
}
