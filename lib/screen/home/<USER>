import 'dart:convert';

import 'package:card_promo/data/model/promo_data.dart';
import 'package:card_promo/res/string/app_strings.dart';
import 'package:card_promo/util/ads_manager.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_utils.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_swipe/flutter_swipe.dart';
import 'package:get/get.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

import '../../app_controller.dart';
import '../../data/model/login_model.dart';
import '../../data/model/promo_detail.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../util/styles.dart';
import '../../widget/bankAndScheme.dart';
import '../../widget/itemPromoWidget.dart';
import '../../widget/line_widget.dart';
import 'home_controller.dart';

class HomeScreen extends GetView<HomeController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Container(
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: Column(
          children: [
            _buildBody(context),
            Obx(() => controller.isLoading.value
                ? Container(
                    // height: 20,
              padding: EdgeInsets.symmetric(vertical: 5),
                    color: AppColors.primary,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                     children: [
                       Text('Đang tải dữ liệu', style: style_S12_W600_WhiteColor,),
                       Container(
                         height: 10,
                         padding: EdgeInsets.all(5),
                         child: CupertinoActivityIndicator(
                           color: AppColors.white,
                           radius: 10,
                         ),
                       )
                     ],
                    ),
                  )
                : SizedBox.shrink()),
            _buildBottomTab(context),
          ],
        ),
      ),
    );
  }

  _buildItemBottom(IconData icon, String title, Function onPress) {
    return TouchableWidget(
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        onPressed: () => onPress(),
        child: Container(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: AppColors.gray,
              ),
              SizedBox(
                height: 3,
              ),
              Text(
                '$title',
                style: style_S12_W600_BlackColor,
              )
            ],
          ),
        ));
  }

  _buildBottomTab(BuildContext context) {
    return Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(bottom: 10, top: 5),
        width: MediaQuery.of(context).size.width,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15),
            topRight: Radius.circular(15),
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.gray1.withOpacity(0.2), // Màu đổ bóng nhẹ
              blurRadius: 1, // Độ mờ của bóng
              spreadRadius: 0.2, // Độ lan rộng của bóng
              offset: Offset(2, -2), // Hướng đổ bóng (đẩy lên trên)
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              child: _buildItemBottom(Icons.map_outlined,
                  AppStrings.getString(AppStrings.tt_recently), () {
                Get.toNamed(AppNameRoute.map_screen);
              }),
            ),
            Flexible(
              child: _buildItemBottom(
                  Icons.store_mall_directory_outlined, AppStrings.getString(AppStrings.tt_store),
                  () {
                UfoLogger().writeLog('click Cửa hàng');
                Get.toNamed(AppNameRoute.merchant_promo_screen);
              }),
            ),

            Flexible(
              child: _buildItemBottom(Icons.settings_sharp,
                  AppStrings.getString(AppStrings.settings), () {
                UfoLogger().writeLog('click Tài khoản');
                Get.toNamed(AppNameRoute.account_screen);
              }),
            ),
            // Flexible(
            //   child: TouchableWidget(
            //       padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            //       onPressed: () {
            //         UfoLogger().writeLog('click thẻ');
            //       },
            //       child: Container(
            //         child: Column(
            //           mainAxisAlignment: MainAxisAlignment.center,
            //           children: [
            //             Image.asset(
            //               fit: BoxFit.fill,
            //               AppImages.ic_hotsale_2,
            //               width: 40,
            //               height: 40,
            //             ),
            //           ],
            //         ),
            //       )),
            // ),
          ],
        ));
  }

  _buildBody(BuildContext context) {
    return Expanded(
      child: Container(
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(bottom: 5, left: 10, right: 10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20), topRight: Radius.circular(20)),
            color: AppColors.background_white),
        child: CustomScrollView(
          controller: controller.scrollController,
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Card Promo',
                          style: style_S20_W600_BlackColor,
                        ),
                        TouchableWidget(
                            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                            onPressed: () => Get.toNamed(AppNameRoute.favourite_screen),
                            child: Container(
                              child: Image.asset(AppImages.ic_star, width: 30, height: 30,),
                            ))
                      ],
                    ),
                  ),

                  Obx(
                    () => (_appController.listBanner.length > 0)
                        ? Container(
                            height: 150,
                            child: GetBuilder<MyAppController>(
                              init: Get.find<MyAppController>(),
                              global: false, // Không khởi tạo lại khi quay lại
                              builder: (ctl) {
                                return ListView.builder(
                                    itemCount:
                                        _appController.listBanner.value.length,
                                    scrollDirection: Axis.horizontal,
                                    itemBuilder: (context, index) {
                                      return Container(
                                        margin: EdgeInsets.only(right: 10),
                                        alignment: Alignment.centerLeft,
                                        child: TouchableWidget(
                                          padding: EdgeInsets.zero,
                                          onPressed: () {
                                            print(
                                                '${_appController.listBanner[index].urlLink}');
                                            controller.onPressBanner(
                                                _appController
                                                    .listBanner[index].urlLink);
                                          },
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                            child: AspectRatio(
                                              aspectRatio: 16 / 10,
                                              child: Image.network(
                                                _appController.listBanner[index]
                                                    .urlBanner,
                                                fit: BoxFit.cover,
                                                loadingBuilder:
                                                    (BuildContext context,
                                                        Widget child,
                                                        ImageChunkEvent?
                                                            loadingProgress) {
                                                  if (loadingProgress == null) {
                                                    return child;
                                                  }
                                                  return Center(
                                                    child:
                                                        LoadingAnimationWidget
                                                            .hexagonDots(
                                                      color: AppColors
                                                          .main_background,
                                                      size: 30,
                                                    ),
                                                  );
                                                },
                                                errorBuilder: (BuildContext
                                                        context,
                                                    Object exception,
                                                    StackTrace? stackTrace) {
                                                  return Center(
                                                    child: Icon(Icons.error),
                                                  );
                                                },
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    });
                              },
                            ))
                        : SizedBox.shrink(),
                  ),

                  SizedBox(
                    height: 20,
                  ),
                  //build category, có thể để thanh 1 row và có icon filler ở bên phải
                  Row(
                    children: [
                      Flexible(
                        flex: 1,
                        child: TouchableWidget(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            Get.toNamed(AppNameRoute.contribute_screen);
                          },
                          child: Container(
                            padding:
                                EdgeInsets.only(top: 10, left: 10, bottom: 10),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 5,
                                  spreadRadius: 2,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.point_of_sale,
                                  color: AppColors.main_background,
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppStrings.getString(
                                            AppStrings.tt_contribute),
                                        style: style_S16_W600_BlackColor,
                                      ),
                                      Text(
                                        AppStrings.getString(
                                            AppStrings.tt_contribute_detail),
                                        style: style_S14_W400_GreyColor,
                                        overflow: TextOverflow.ellipsis,
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Flexible(
                        flex: 1,
                        child: TouchableWidget(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            Get.toNamed(AppNameRoute.regist_card_screen);
                          },
                          child: Container(
                            padding:
                                EdgeInsets.only(top: 10, left: 10, bottom: 10),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 5,
                                  spreadRadius: 2,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.add_card_outlined,
                                  color: AppColors.main_background,
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        AppStrings.getString(
                                            AppStrings.tt_register_card),
                                        style: style_S16_W600_BlackColor,
                                      ),
                                      Text(
                                        AppStrings.getString(
                                            AppStrings.tt_register_card_detail),
                                        style: style_S14_W400_GreyColor,
                                        overflow: TextOverflow.ellipsis,
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
            SliverPersistentHeader(
                pinned: true,
                delegate: _SliverHeaderDelegate(
                    minHeight: 100,
                    maxHeight: 100,
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      color: AppColors.white,
                      child: Column(
                        children: [
                          _buildCardsWidget(),
                          _buildCategoryWidget(),
                        ],
                      ),
                    ))),
            Obx(
              () => SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
                  if (index == controller.lsPromoShow.length) {
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: 16),
                      child: Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }

                  PromoDetail data = controller.lsPromoShow[index];
                  return Container(
                    margin: EdgeInsets.symmetric(vertical: 5),
                    child: TouchableWidget(
                      borderRadiusEffect: BorderRadius.circular(15),
                      padding: EdgeInsets.zero,
                      onPressed: () {
                        Future.delayed(Duration(milliseconds: 100), () {
                          if (data != null) {
                            Get.toNamed(
                              AppNameRoute.promo_detai_screen,
                              arguments: jsonEncode(data),
                            );
                          }
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          border: Border.all(
                            color: AppColors.lightGreyText,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  isNullEmpty(data.iconBank)
                                      ? Icon(
                                          Icons.account_balance,
                                          color: AppColors.main_background,
                                        )
                                      : SvgPicture.network(
                                          data.iconBank!,
                                          fit: BoxFit.fill,
                                          width: 35,
                                          height: 35,
                                          placeholderBuilder: (context) => Icon(
                                            Icons.account_balance,
                                            color: AppColors.primary,
                                          ),
                                        ),
                                  SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${data.title}',
                                          style: style_S16_W600_BlackColor,
                                          maxLines: 2,
                                        ),
                                        SizedBox(
                                          height: 5,
                                        ),
                                        BankandschemeWidget(
                                          bank: '',
                                          scheme: data.scheme ?? [],
                                        ),
                                      ],
                                    ),
                                  ),
                                ]),
                            SizedBox(
                              height: 5,
                            ),
                            Text(
                              maxLines: 2,
                              '${data.description?.replaceAll('/n', '\n')}',
                              style: style_S14_W400_BlackColor,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Container(
                              child: MySeparator(
                                height: 0.5,
                                color: AppColors.gray2,
                              ),
                              padding: EdgeInsets.symmetric(vertical: 5),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text(
                                  '${AppStrings.getString(AppStrings.tt_end_date)}: ${data.endDateAsDate}',
                                  style: style_S14_W400_BlackColor,
                                ),
                                Container(
                                  child: Text(
                                    AppStrings.getString(AppStrings.tt_detail),
                                    style: style_S14_W600_MainColor,
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                }, childCount: controller.lsPromoShow.length),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryWidget() {
    return Container(
      height: 40,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Obx(
              () => ListView.builder(
                padding: EdgeInsets.zero,
                scrollDirection: Axis.horizontal,
                itemCount: controller.listCategory.length,
                itemBuilder: (context, index) {
                  bool isSelected =
                      controller.selectedIndexCategory.value == index;
                  return GestureDetector(
                    onTap: () {
                      controller.selectCategory(index);
                    },
                    child: Obx(
                      () => Container(
                        alignment: Alignment.center,
                        margin: EdgeInsets.symmetric(horizontal: 5),
                        decoration: BoxDecoration(
                            // borderRadius: BorderRadius.circular(10),
                            // border: Border.all(color: Colors.grey, width: 1),
                            ),
                        child: Text('${controller.listCategory.value[index]}',
                            style: TextStyle(
                                color: isSelected ? Colors.black : Colors.grey,
                                fontWeight: FontWeight.w600,
                                fontFamily: kFontFamilyBeVietnamPro,
                                fontSize: isSelected ? 16 : 14)),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildCardsWidget() {
    return Container(
      height: 40,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Obx(
              () => ListView.builder(
                padding: EdgeInsets.zero,
                scrollDirection: Axis.horizontal,
                itemCount: controller.listBankSaved.length,
                itemBuilder: (context, index) {
                  bool isSelected = controller.selectedIndexCard.value == index;
                  return GestureDetector(
                    onTap: () {
                      controller.selectCard(index);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.symmetric(horizontal: 5),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            '${controller.listBankSaved.value[index].name}${!isNullEmpty(controller.listBankSaved.value[index].scheme) ? '-${controller.listBankSaved.value[index].scheme}' : ''}',
                            style: TextStyle(
                                color: isSelected ? Colors.black : Colors.grey,
                                fontWeight: FontWeight.w600,
                                fontFamily: kFontFamilyBeVietnamPro,
                                fontSize: isSelected && isNullEmpty(controller.listBankSaved.value[index].pan) ? 16 : 14),
                          ),

                          isNullEmpty(controller.listBankSaved.value[index].pan) ? SizedBox.shrink() : Text(
                            '${controller.listBankSaved.value[index].pan}',
                            style: TextStyle(
                                color: isSelected ? Colors.black : Colors.grey,
                                fontWeight: FontWeight.w400,
                                fontFamily: kFontFamilyBeVietnamPro,
                                fontSize: 14),
                          ),

                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          TouchableWidget(
            padding: EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              color: AppColors.main_background,
              borderRadius: BorderRadius.circular(15),
            ),
            onPressed: () {
              controller.addCardBank();
            },
            child: Row(
              children: [
                Text(
                  AppStrings.getString(AppStrings.tt_add_card),
                  style: style_S12_W600_WhiteColor,
                ),
                SizedBox(
                  width: 5,
                ),
                Icon(
                  size: 16,
                  Icons.add,
                  color: AppColors.white,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Delegate cho SliverPersistentHeader
class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _SliverHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(covariant _SliverHeaderDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
