import 'package:card_promo/util/styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/font/app_fonts.dart';
import '../../res/image/app_images.dart';
import '../../res/string/app_strings.dart';
import '../../widget/common_text_field.dart';
import '../../widget/touchable_widget.dart';
import 'login_controller.dart';

class LoginScreen extends GetView<LoginController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Scaffold(
        backgroundColor: AppColors.background_green,
        body: Stack(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              child: Container(child: SvgPicture.asset(AppImages.ic_header)),
            ),
            Container(
              width: MediaQuery.of(context).size.width,
              margin:
                  EdgeInsets.only(top: MediaQuery.of(context).size.height / 8),
              padding: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20)),
                  color: AppColors.background_white),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                      child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        'Card Promo',
                        style: style_S30_W600_BlackColor,
                      ),
                    ],
                  )),
                  SizedBox(
                    height: 50,
                  ),
                  Obx(
                    () => Text(
                      controller.modeSignUp.value ? 'Đăng ký' : 'Đăng nhập',
                      style: style_S20_W600_BlackColor,
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  CommonTextField(
                    height: 60,
                    fontFamily: kFontFamilyBeVietnamPro,
                    controller: controller.userController,
                    hintText: AppStrings.getString(AppStrings.email) ?? '',
                    hintTextFontSize: 16,
                    maxLength: 20,
                    isBorder: true,
                    hideUnderBorderLine: true,
                    onChanged: (value) {
                      controller.onInputUserAndPass();
                    },
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Obx(
                    () => CommonTextField(
                      height: 60,
                      fontFamily: kFontFamilyBeVietnamPro,
                      controller: controller.passController,
                      hintText: AppStrings.getString(AppStrings.password) ?? '',
                      hintTextFontSize: 16,
                      maxLength: 20,
                      obscureText: !controller.isShowPassword.value,
                      hideUnderBorderLine: true,
                      isBorder: true,
                      onChanged: (value) {
                        controller.onInputUserAndPass();
                      },
                      suffix: TouchableWidget(
                        width: 30,
                        height: 20,
                        padding: EdgeInsets.all(0),
                        child: Image.asset(
                          AppImages.icEye,
                          width: 19,
                          height: 12,
                        ),
                        onPressed: controller.onPressShowHidePassword,
                      ),
                    ),
                  ),

                  Obx(
                    () => controller.modeSignUp.value
                        ? Column(
                            children: [
                              SizedBox(
                                height: 15,
                              ),
                              CommonTextField(
                                height: 60,
                                fontFamily: kFontFamilyBeVietnamPro,
                                controller: controller.passStep2Controller,
                                hintText: AppStrings.getString(
                                        AppStrings.passwordStep2) ??
                                    '',
                                hintTextFontSize: 16,
                                maxLength: 20,
                                obscureText: !controller.isShowPassword.value,
                                hideUnderBorderLine: true,
                                isBorder: true,
                                onChanged: (value) {
                                  controller.onInputUserAndPass();
                                },
                                suffix: TouchableWidget(
                                  width: 30,
                                  height: 20,
                                  padding: EdgeInsets.all(0),
                                  child: Image.asset(
                                    AppImages.icEye,
                                    width: 19,
                                    height: 12,
                                  ),
                                  onPressed: controller.onPressShowHidePassword,
                                ),
                              ),
                            ],
                          )
                        : SizedBox.shrink(),
                  ),

                  SizedBox(
                    height: 10,
                  ),
                  Obx(
                    () => controller.modeSignUp.value
                        ? SizedBox.shrink()
                        : Container(
                            alignment: Alignment.topRight,
                            child: Text(
                              'Quên mật khẩu?',
                              style: style_S16_W600_BlueColor,
                            ),
                          ),
                  ),

                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Obx(
                        () => TouchableWidget(
                            height: 50,
                            decoration: BoxDecoration(
                              color: controller.enableBtnContinue.value
                                  ? AppColors.tabSelected
                                  : AppColors.tabUnSelected,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            borderRadiusEffect: BorderRadius.circular(10),
                            onPressed: () {
                              controller.onPressContinue();
                            },
                            child: Container(
                              child: Text(
                                controller.modeSignUp.value
                                    ? 'Đăng ký'
                                    : 'Đăng nhập',
                                style: controller.enableBtnContinue.value
                                    ? style_S18_W600_WhiteColor
                                    : style_S18_W600_BlackColor,
                              ),
                            )),
                      ),
                      Obx(
                        () => controller.modeSignUp.value
                            ? TouchableWidget(
                                padding: EdgeInsets.zero,
                                onPressed: () {
                                  controller.changeModeSignUp(false);
                                },
                                child: Text(
                                  'Đăng nhập',
                                  style: style_S16_W600_BlueColor,
                                ))
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    'Đăng ký ',
                                    style: style_S16_W400_BlackColor,
                                  ),
                                  TouchableWidget(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        controller.changeModeSignUp(true);
                                      },
                                      child: Text(
                                        'tại đây',
                                        style: style_S16_W600_BlueColor,
                                      ))
                                ],
                              ),
                      )
                    ],
                  )),

                  // Container(
                  //   child: Column(
                  //     mainAxisAlignment: MainAxisAlignment.start,
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       Text('Sử dụng thẻ thông minh hơn', style: style_S14_W400_BlackColor,),
                  //       Text('Tiết kiệm hơn', style: style_S14_W400_BlackColor,),
                  //       Text('Áp dụng khuyến mãi để: Mua sắm, đóng học phí, đóng tiền điện, nước, ...', style: style_S14_W400_BlackColor,),
                  //     ],
                  //   ),
                  // ),

                  Container(
                    height: 100,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Flexible(
                            flex: 1,
                            child: _buildContact(
                                AppImages.ic_phone, '0332193377')),
                        Container(
                          width: 1,
                          color: AppColors.gray2,
                          height: 40,
                        ),
                        Flexible(
                            flex: 1,
                            child:
                                _buildContact(AppImages.ic_zalo, '0332193377')),
                        Container(
                          width: 1,
                          color: AppColors.gray2,
                          height: 40,
                        ),
                        Flexible(
                            flex: 1,
                            child: _buildContact(
                                AppImages.ic_email, '<EMAIL>')),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    alignment: Alignment.center,
                    child: Text('Version: 1.0.0'),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  _buildContact(String image, String title) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(image),
          SizedBox(
            height: 5,
          ),
          Text(
            title,
            style: style_S14_W400_BlackColor,
            textAlign: TextAlign.center,
          )
        ],
      ),
    );
  }
}
