import 'dart:convert';

import 'package:card_promo/util/api_constant.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/base_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../app_controller.dart';
import '../../data/model/login_model.dart';
import '../../data/model/user.dart';
import '../../data/provider/base_service.dart';
import '../../util/app_utils.dart';

class LoginBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> LoginController(), fenix: false);
  }
}

class LoginController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;
  TextEditingController userController = TextEditingController();
  TextEditingController passController = TextEditingController();
  TextEditingController passStep2Controller = TextEditingController();
  RxBool isShowPassword = false.obs;
  RxBool enableBtnContinue = false.obs;
  RxBool modeSignUp = false.obs;

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();

  }

  onInputUserAndPass() {
    final bool isUserAndPassFilled = userController.text.isNotEmpty && passController.text.isNotEmpty;
    enableBtnContinue.value = modeSignUp.value
        ? isUserAndPassFilled && passStep2Controller.text.isNotEmpty
        : isUserAndPassFilled;
  }

  onPressShowHidePassword() {
    isShowPassword.value = !isShowPassword.value;
  }

  onPressContinue() {
    UfoLogger().writeLog('onPress continue ${modeSignUp.value ? 'SignUp' : 'SignIn'}');
    if (modeSignUp.value) {
      onSignUp();
    }else {
      onSignIn();
    }
  }

  void changeModeSignUp(bool value) {
    UfoLogger().writeLog('Sign Up: $value');
    modeSignUp.value = value;
  }

  void onSignUp() async{
    if (checkValidDataSignUp()) {
      Map params = {
        'userName': userController.text.trim(),
        'password': passController.text.trim()
      };
      _appController.showLoading();
      var response = await BaseService().sendData(ApiConstant.urlSignUp, jsonEncode(params));
      _appController.hideLoading();
      if (response.errorResponse.code == 200 && response.responseData.isNotEmpty) {
        User? user = AppUtils.parseResponse<User>(context, response.responseData, User.fromJson);
        if (user != null) {
          AppUtils.showDialogAlert(
            onPress1stButton: () {
              modeSignUp.value = false;
            },
            context,
            type: BaseDialog.TYPE_SUCCESS,
            title: 'Thành công',
            description: 'Tạo tài khoản ${user.userName} thành công, đăng nhâp để xem khuyến mãi.',
          );
        }
      } else {
        AppUtils.showDialogAlert(
          context,
          type: BaseDialog.TYPE_ERROR,
          title: 'Có lỗi xảy ra, Vui lòng kiểm tra kết nối mạng và thử lại.',
          description: '${response.errorResponse.message}',
        );
      }
    }
  }

  void onSignIn() async {
    //todo test
    var response = AppUtils.getLoginData();
    LoginModel? loginModel = AppUtils.parseResponse<LoginModel>(context, response, LoginModel.fromJson);
    if (loginModel != null) {
      BaseService().authorizationHeader = loginModel.token;
      _appController.loginModel = loginModel;
      UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_ACTION, 'goto home screen');
      Get.offAllNamed(AppNameRoute.home_screen);
    }
    return;



    if (checkValidDataSignIn()) {
      Map params = {
        'userName': userController.text.trim(),
        'password': passController.text.trim()
      };
      _appController.showLoading();
      var response = await BaseService().sendData(ApiConstant.urlSignIn, jsonEncode(params));
      _appController.hideLoading();
      if (response.errorResponse.code == 200 && response.responseData.isNotEmpty) {
        LoginModel? loginModel = AppUtils.parseResponse<LoginModel>(context, response.responseData, LoginModel.fromJson);
        if (loginModel != null) {
          BaseService().authorizationHeader = loginModel.token;
          _appController.loginModel = loginModel;
          UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_ACTION, 'goto home screen');
          Get.offAllNamed(AppNameRoute.home_screen);
        }
      }else {
        AppUtils.showDialogAlert(
          context,
          type: BaseDialog.TYPE_ERROR,
          title: 'Có lỗi xảy ra',
          description: '${response.errorResponse.message}',
        );
      }
    }
  }

  bool checkValidDataSignUp() {
    String? errorMsg;
    String email = userController.text.trim();
    errorMsg = checkEmptyAndValidEmail(email);
    if (errorMsg != null) {
      AppUtils.showDialogAlert(context, type: BaseDialog.TYPE_WARNING, title: errorMsg,);
      return false;
    }
    String pass = passController.text.trim();
    String pass2 = passStep2Controller.text.trim();
    if (pass != pass2) {
      AppUtils.showDialogAlert(context, type: BaseDialog.TYPE_WARNING, title: 'Mật khẩu không trùng khớp!');
      return false;
    }
    return true;
  }

  bool checkValidDataSignIn() {
    String? errorMsg;
    String email = userController.text.trim();
    errorMsg = checkEmptyAndValidEmail(email);
    if (errorMsg != null) {
      AppUtils.showDialogAlert(context, type: BaseDialog.TYPE_WARNING, title: errorMsg,);
      return false;
    }
    String pass = passController.text.trim();
    if (pass.isEmpty) {
      AppUtils.showDialogAlert(context, type: BaseDialog.TYPE_WARNING, title: 'Mật khẩu không được trống!');
      return false;
    }
    return true;
  }
}