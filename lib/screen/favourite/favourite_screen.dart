import 'package:card_promo/res/string/app_strings.dart';
import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/screen/detail_promo/promo_detail_controller.dart';
import 'package:card_promo/screen/favourite/favourite_controller.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../data/model/promo_detail.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../util/app_dimens.dart';
import '../../util/styles.dart';
import '../../widget/itemPromoWidget.dart';
import '../../widget/line_widget.dart';

class FavouriteScreen extends GetView<FavouriteController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.main_background,
      body: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            child: Container(child: SvgPicture.asset(AppImages.ic_header)),
          ),
          Column(children: [
            Container(
              margin: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top + 10,
                  left: 10,
                  bottom: 10,
                  right: 10),
              alignment: Alignment.centerLeft,
              // height: MediaQuery.of(context).size.height / 8,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  TouchableWidget(
                      padding: EdgeInsets.all(5),
                      onPressed: () {
                        Get.back();
                      },
                      child: Icon(
                        Icons.close,
                        color: AppColors.white,
                      )),
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: Text(
                      AppStrings.getString(AppStrings.tt_favourite_promo),
                      style: style_S18_W600_WhiteColor,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Container(
                  width: MediaQuery.of(context).size.width,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                      color: AppColors.background_white),
                  child: Obx(() => (controller.listPromo.value.length == 0)
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Image.asset(
                                AppImages.ic_dialog_warning,
                                width: AppDimens.icon60,
                                height: AppDimens.icon60,
                              ),
                              SizedBox(
                                height: 10,
                              ),
                              Text(
                                'Không có dữ liệu',
                                style: style_S16_W400_BlackColor,
                              ),
                            ],
                          ),
                        )
                      :  ItemPromoWidget(lsPromoShow: controller.listPromo.value,))),
            ),
          ])
        ],
      ),
    );
  }
}
