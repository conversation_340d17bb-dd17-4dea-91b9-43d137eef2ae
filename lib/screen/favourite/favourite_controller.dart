import 'dart:convert';

import 'package:card_promo/util/local_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../data/model/promo_detail.dart';
import '../../util/app_route.dart';

class FavouriteBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> FavouriteController(), fenix: false);
  }
}

class FavouriteController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;
  // Future<List<PromoDetail>?> getAllFavouritePromo(
  RxList<PromoDetail> listPromo = <PromoDetail>[].obs;
  
  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
    initData();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();
  }

  void initData() async {
    List<PromoDetail>? promoSaved = await LocalStorage().getAllFavouritePromo();
    if (promoSaved != null) {
      listPromo.value = promoSaved;
    }
    listPromo.refresh();
  }

  void onPressDetailPromo(String? id) {
    for (PromoDetail promo in listPromo) {
      if (promo.id == id) {
        Get.toNamed(AppNameRoute.promo_detai_screen, arguments: jsonEncode(promo),)!.then((_) {
          initData();
        });
        break;
      }
    }
  }
}