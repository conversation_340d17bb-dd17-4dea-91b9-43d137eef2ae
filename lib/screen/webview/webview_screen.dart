import 'package:card_promo/screen/webview/webview_controller.dart';
import 'package:card_promo/util/styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/font/app_fonts.dart';
import '../../res/image/app_images.dart';
import '../../res/string/app_strings.dart';
import '../../widget/common_text_field.dart';
import '../../widget/touchable_widget.dart';

class WebviewScreen extends GetView<WebviewController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;

    return Scaffold(
      backgroundColor: AppColors.main_background,
      body: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            child: Container(child: SvgPicture.asset(AppImages.ic_header)),
          ),

          Column(
            children: [
              Container(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top,
                  left: 10,
                ),
                alignment: Alignment.centerLeft,
                height: MediaQuery.of(context).size.height / 9,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TouchableWidget(
                        padding: EdgeInsets.symmetric(
                            horizontal: 10, vertical: 5),
                        onPressed: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.close,
                          color: AppColors.white,
                        )),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Text(
                        'Chương trình khuyến mãi',
                        style: style_S18_W600_WhiteColor,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: _buildWebview(context),
              ),
            ],
          )
        ],
      ),
    );
  }
  _buildWebview(BuildContext context) {
    return Expanded(
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: WebViewWidget(
              controller: controller.webViewController,
            ),
          ),
          Container(
            padding: EdgeInsets.only(
                top: 5,
                left: 10,
                right: 10,
                bottom: 5 +
                    MediaQuery.of(context).padding.bottom),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6)),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(57, 82, 107, 0.09),
                  offset: Offset(0, -4),
                  blurRadius: 25,
                )
              ],
            ),
            child: Row(
              children: <Widget>[
                Expanded(
                  flex: 1,
                  child: TouchableWidget(
                    onPressed: controller.onPressWVBack,
                    padding: EdgeInsets.all(0),
                    child: Container(
                        height: 40,
                        child: Icon(
                          Icons.arrow_back,
                          color:  AppColors.blue,
                        )),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: TouchableWidget(
                    onPressed: controller.onPressWVReload,
                    padding: EdgeInsets.all(0),
                    child: Container(
                        height: 40,
                        child: Icon(
                          Icons.refresh,
                          color: AppColors.blue,
                        )),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: TouchableWidget(
                    onPressed: controller.onPressWVNext,
                    padding: EdgeInsets.all(0),
                    child: Container(
                        height: 40,
                        child: Icon(
                          Icons.arrow_forward,
                          color: AppColors.blue,
                        )),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
