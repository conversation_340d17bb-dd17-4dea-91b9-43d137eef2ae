import 'dart:convert';

import 'package:card_promo/util/api_constant.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/base_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../data/model/login_model.dart';
import '../../data/model/user.dart';

import '../../util/app_utils.dart';

class WebviewBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> WebviewController(), fenix: false);
  }
}

class WebviewController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;
  WebViewController webViewController = WebViewController();

  RxString link = ''.obs;

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
//https://promo.highlandscoffee.com.vn/uudai2
//     link.value = Get.arguments;
    link.value = 'https://promo.highlandscoffee.com.vn/uudai2';
    _initWebViewController();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();
    _appController.showLoading(autoHide: true);
  }

  _initWebViewController(){
    final PlatformWebViewControllerCreationParams params = const
    PlatformWebViewControllerCreationParams();
    webViewController = WebViewController.fromPlatformCreationParams(params);
    webViewController
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            debugPrint('WebView is loading (progress : $progress%)');
          },
          onPageStarted: (String url) {
            debugPrint('Page started loading: $url');
            onPageStarted(url);
          },
          onPageFinished: (String url) {
            debugPrint('Page finished loading: $url');
            onPageFinished(url);
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('''
              Page resource error:
                code: ${error.errorCode}
                description: ${error.description}
                errorType: ${error.errorType}
                isForMainFrame: ${error.isForMainFrame}
                        ''');
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              debugPrint('blocking navigation to ${request.url}');
              return NavigationDecision.prevent;
            }
            debugPrint('allowing navigation to ${request.url}');
            return NavigationDecision.navigate;
          },
          onUrlChange: (UrlChange change) {
            debugPrint('url change to ${change.url}');
          },
        ),
      )
      ..loadRequest(Uri.parse(link.value));
  }


  onPressWVBack() async {
    bool canGoBack = await webViewController.canGoBack();
    if (canGoBack) webViewController.goBack();
  }

  onPressWVReload() async {
    webViewController.reload();
  }

  onPressWVNext() async {
    bool canGoBack = await webViewController.canGoForward();
    if (canGoBack) webViewController.goForward();
  }

  onPageStarted(String url) {
    // isLoadingWeb.value = true;
  }

  onPageFinished(String url) async {
    // isLoadingWeb.value = false;
    _appController.hideLoading();
  }
}