import 'dart:convert';

import 'package:card_promo/util/local_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../data/model/promo_detail.dart';
import '../../util/ads_manager.dart';
import '../../util/app_route.dart';
import '../../widget/promo_share_card.dart';

class PromoDetailBiding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> PromoDetailController(), fenix: false);
  }
}

class PromoDetailController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  RxString linkPromo = ''.obs;

  Rx<PromoDetail> promo = PromoDetail(active: true, sendNotification: false).obs;

  RxBool isSave = false.obs;
  RxBool isLoadedBanner = false.obs;
  var adsManager = AdsManager();

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
    try {
      promo.value = PromoDetail.fromJson(jsonDecode(Get.arguments));
    }catch(e){
      e.printError();
    }

    if ((_appController.user.value.contributionPoints ?? 0) < 1 && (_appController.config.value.isShowAds == '1')) {
      adsManager.createBanner(() {
        isLoadedBanner.value = true;
      });
    }
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();

   initFavourite();
  }

  changeSaveState() async {
    if (isSave.value) {
      await LocalStorage().removeFavouritePromo(promo.value);
    } else {
      await LocalStorage().saveFavouritePromo(promo.value);
    }
    isSave.value = !isSave.value;
  }

  void initFavourite() async {
    PromoDetail? promoSaved = await LocalStorage().getDetailFavouritePromo(promo.value);
    if (promoSaved?.id == promo.value.id) {
      isSave.value = true;
    }
  }

  void onPressDetailLinkPromo() {
    Get.toNamed(AppNameRoute.webview_screen, arguments: promo.value.linkPromo);
  }

  void onPressSharePromo() {
    showDialog(
      context: context,
      builder: (_) => PromoShareCard(promo: promo.value),
    );
  }

}