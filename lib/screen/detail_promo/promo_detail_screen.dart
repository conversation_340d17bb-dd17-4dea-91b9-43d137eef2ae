import 'package:card_promo/res/string/app_strings.dart';
import 'package:card_promo/screen/detail_promo/promo_detail_controller.dart';
import 'package:card_promo/util/app_utils.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:intl/intl.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../util/app_validation.dart';
import '../../util/styles.dart';
import '../../util/ufo_logger.dart';
import '../../widget/bankAndScheme.dart';
import '../../widget/banner_app.dart';
import '../../widget/expandble_text.dart';

class PromoDetailScreen extends GetView<PromoDetailController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.main_background,
      body: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            child: Container(child: SvgPicture.asset(AppImages.ic_header)),
          ),
          Column(
            children: [
              Container(
                margin: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 10,
                    left: 10,
                    bottom: 10,
                    right: 10),
                // color: Colors.amber,
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TouchableWidget(
                        padding: EdgeInsets.all(5),
                        onPressed: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.close,
                          color: AppColors.white,
                        )),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Text(
                        AppStrings.getString(AppStrings.tt_promo_detail),
                        style: style_S18_W600_WhiteColor,
                      ),
                    ),
                    Obx(
                      () => TouchableWidget(
                          padding: EdgeInsets.all(5),
                          borderRadiusEffect:
                              BorderRadius.all(Radius.circular(20)),
                          onPressed: () {
                            controller.changeSaveState();
                          },
                        child: Image.asset(
                            controller.isSave.value
                                ? AppImages.ic_star
                                : AppImages.ic_star_not_check,
                            width: 25,
                            height: 25,
                            color: AppColors.white,
                          )
                      ),
                    )
                  ],
                ),
              ),
              Expanded(
                child: Obx(
                  () => Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(20),
                              topRight: Radius.circular(20)),
                          color: AppColors.background_white),
                      child: Column(
                        children: [
                          Obx(() => controller.isLoadedBanner.value
                              ? Align(
                            alignment: Alignment.bottomCenter,
                            child: Container(
                              width: controller.adsManager.bannerAd!.size.width
                                  .toDouble(),
                              height: controller.adsManager.bannerAd!.size.height
                                  .toDouble(),
                              child: AdWidget(ad: controller.adsManager.bannerAd!),
                            ),
                          )
                              : SizedBox.shrink()),
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    margin: EdgeInsets.symmetric(vertical: 10),
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                        '${controller.promo.value.title}',
                                        style: TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.bold)),
                                  ),
                                  // SizedBox(height: 10,),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 5),
                                    decoration: BoxDecoration(
                                        color: AppColors.grayBorderQr,
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Text(
                                      '${controller.promo.value.category?.join(", ")}',
                                      style: style_S14_W400_BlackColor,
                                    ),
                                  ),

                                  (controller.promo.value.promoImageUrl ==
                                              null ||
                                          controller.promo.value.promoImageUrl!
                                              .isEmpty)
                                      ? SizedBox.shrink()
                                      : Image.network(
                                          controller.promo.value.promoImageUrl!,
                                          errorBuilder:
                                              (context, error, stackTrace) =>
                                                  SizedBox.shrink(),
                                        ),
                                  ListTile(
                                      leading: (controller
                                                      .promo.value.iconBank ==
                                                  null ||
                                              controller.promo.value.iconBank!
                                                  .isEmpty)
                                          ? Icon(
                                              Icons.account_balance,
                                              color: AppColors.main_background,
                                            )
                                          : SvgPicture.network(
                                              controller.promo.value.iconBank!,
                                              fit: BoxFit.contain,
                                              width: 35,
                                              height: 35,
                                              placeholderBuilder: (context) =>
                                                  Icon(
                                                Icons.account_balance,
                                                color: AppColors.primary,
                                              ),
                                            ),

                                      //     : Image.network(
                                      //         controller.promo.value.iconBank ??
                                      //             '',
                                      //         width: 40,
                                      //         height: 40, errorBuilder: (context, error, stackTrace) => Icon(
                                      //   Icons.account_balance,
                                      //   color: AppColors.primary,
                                      // ),),
                                      title: Container(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 5),
                                        child: Text(
                                          '${controller.promo.value.bankName}',
                                          style: style_S14_W600_BlackColor,
                                        ),
                                      ),
                                      subtitle: BankandschemeWidget(
                                        bank: '',
                                        scheme:
                                            controller.promo.value.scheme ?? [],
                                      )
                                      // subtitle: Text(
                                      //   '${controller.promo.value.scheme?.join(", ")}',
                                      //   style: style_S14_W400_BlackColor,
                                      // ),
                                      ),

                                  controller.promo.value.bins?.length == 0
                                      ? SizedBox.shrink()
                                      : Text(
                                          '${controller.promo.value.scheme?.join(", ")}',
                                          style: style_S14_W400_BlackColor,
                                        ),

                                  Container(
                                    margin: EdgeInsets.symmetric(vertical: 5),
                                    child: (controller.promo.value.promoCode ==
                                                null ||
                                            controller
                                                .promo.value.promoCode!.isEmpty)
                                        ? SizedBox.shrink()
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Text(
                                                "Mã: ",
                                                style:
                                                    style_S14_W400_BlackColor,
                                              ),
                                              Text(
                                                "${controller.promo.value.promoCode}",
                                                style:
                                                    style_S20_W600_BlackColor,
                                              ),
                                            ],
                                          ),
                                  ),
                                  _widgetOptionPromo(),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  _itemDetail(AppStrings.getString(AppStrings.tt_start_date_full),
                                      '${formatDate(controller.promo.value.startDate)}',
                                      icon: Icons.calendar_today),
                                  _itemDetail(AppStrings.getString(AppStrings.tt_end_date_full),
                                      '${formatDate(controller.promo.value.endDate)}',
                                      icon: Icons.calendar_today),
                                  SizedBox(
                                    height: 5,
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 5),
                                    child: controller.promo.value.active
                                        ? Text("🟢 ${AppStrings.getString(AppStrings.tt_in_progess)}")
                                        : Text("❌ ${AppStrings.getString(AppStrings.tt_exprite_date)}"),
                                  ),

                                  // Divider(),
                                  BannerApp(),

                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 5, vertical: 5),
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.store,
                                              color: AppColors.main_background,
                                            ),
                                            SizedBox(
                                              width: 5,
                                            ),
                                            Text(
                                              "${controller.promo.value.merchantName}",
                                              style: style_S16_W600_BlackColor,
                                            )
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Text(
                                            '${AppStrings.getString(AppStrings.tt_location_at)}: ${controller.promo.value.storeLocation}')
                                      ],
                                    ),
                                  ),

                                  Divider(),

                                  ExpandableText(
                                    text:
                                        '${controller.promo.value.description?.replaceAll('/n', '\n')}',
                                    style: style_S14_W400_BlackColor,
                                    maxLines: 5,
                                  ),

                                  !isNullOrEmpty(controller
                                          .promo.value.userNameContribution)
                                      ? Text(
                                          "Người đóng góp: ",
                                          style: style_S16_W600_BlueColor,
                                        )
                                      : SizedBox.shrink(),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 10),
                            child: Row(
                              children: [
                                Flexible(
                                  flex: 1,
                                  child: TouchableWidget(
                                      margin:
                                          EdgeInsets.symmetric(horizontal: 5),
                                      decoration: BoxDecoration(
                                          color: AppColors
                                              .primary,
                                          borderRadius:
                                              BorderRadius.circular(10)),
                                      onPressed: () {
                                        // controller.changeModeSignUp(false);
                                        controller.onPressSharePromo();
                                      },
                                      child: Text(
                                        AppStrings.getString(AppStrings.tt_share_promo),
                                        style: style_S16_W600_WhiteColor,
                                      )),
                                ),
                                isNullOrEmpty(controller.promo.value.linkPromo)
                                    ? SizedBox.shrink()
                                    : Flexible(
                                        flex: 1,
                                        child: TouchableWidget(
                                            margin: EdgeInsets.symmetric(
                                                horizontal: 5),
                                            decoration: BoxDecoration(
                                                color: AppColors.main_background,
                                                borderRadius:
                                                    BorderRadius.circular(10)),
                                            onPressed: () {
                                              // controller.changeModeSignUp(false);
                                              controller
                                                  .onPressDetailLinkPromo();
                                            },
                                            child: Text(
                                              AppStrings.getString(AppStrings.tt_rule_promo),
                                              style: style_S16_W600_WhiteColor,
                                            )),
                                      )
                              ],
                            ),
                          ),
                        ],
                      )),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  formatDate(int? date) {
    if (date == null || date == 0) return "-"; // Trả về "N/A" nếu ngày null

    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(date);
    return DateFormat('dd-MM-yyyy').format(dateTime);
  }

  launchURL(String? linkPromo) {}

  Widget _itemDetail(String title, String value, {IconData? icon}) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 5),
      padding: EdgeInsets.symmetric(horizontal: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              icon != null
                  ? Icon(icon, color: AppColors.gray1, size: 18)
                  : SizedBox.shrink(),
              SizedBox(
                width: 5,
              ),
              Text(
                '${title}: ',
                style: style_S14_W400_BlackColor,
              )
            ],
          ),
          Text(
            value,
            style: style_S16_W600_BlackColor,
          )
        ],
      ),
    );
  }

  Widget _widgetOptionPromo() {
    bool isReduce = (controller.promo.value.promoOptionAmountReduce?.isNotEmpty ?? false);
    bool isRefund = (controller.promo.value.promoOptionAmountRefund?.isNotEmpty ?? false);

    if (isReduce || isRefund) {
      // Xử lý khi có ít nhất một giá trị không rỗng
      return Container(
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.only(top: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              spreadRadius: 2,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Ưu đãi khuyến mãi",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 5),
            isReduce ?
            Column(
              children:
              controller.promo.value.promoOptionAmountReduce!.map((promo) {
                String promoText;
                if (promo.fixAmountReduce > 0) {
                  promoText =
                  "Giảm ${AppUtils.formatCurrency(promo.fixAmountReduce ~/ 1000)}K cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountReduce > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountReduce ~/ 1000)}K' : ''}";
                } else {
                  promoText =
                  "Giảm ${promo.rateAmountReduce}% cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountReduce > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountReduce ~/ 1000)}K' : ''}";
                }
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                          Icons.local_offer, color: Colors.redAccent, size: 18),
                      SizedBox(width: 6),
                      Text(
                        promoText,
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ) : Column(
              children:
              controller.promo.value.promoOptionAmountRefund!.map((promo) {
                String promoText;
                if (promo.fixAmountRefund > 0) {
                  promoText =
                  "Hoàn ${AppUtils.formatCurrency(promo.fixAmountRefund ~/ 1000)}K cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountRefund > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountRefund ~/ 1000)}K' : ''}";
                } else {
                  promoText =
                  "Hoàn ${promo.rateAmountRefund}% cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountRefund > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountRefund ~/ 1000)}K' : ''}";
                }
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                          Icons.local_offer, color: Colors.redAccent, size: 18),
                      SizedBox(width: 6),
                      Text(
                        promoText,
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }
}
