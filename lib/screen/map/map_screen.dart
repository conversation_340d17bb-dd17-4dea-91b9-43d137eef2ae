import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

import '../../app_controller.dart';
import '../../loading_widget.dart';
import '../../res/color/app_colors.dart';
import '../../util/styles.dart';
import '../../widget/MerchantListNearMeWidget.dart';
import '../../widget/detail_location_widget.dart';
import 'map_controller.dart';

class MapScreen extends GetView<MapController> {
  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      body: Stack(
        children: [
          Obx(
            () => AnimatedContainer(
              duration: Duration(milliseconds: 50),
              height: controller.mapHeight.value,
              child: GoogleMap(
                onMapCreated: (GoogleMapController ggMapController) {
                  controller.onCompleteMapCreated(ggMapController);
                },
                initialCameraPosition: CameraPosition(
                  target: LatLng(21.0278, 105.8342), // Mặc định: Hà Nội
                  zoom: 11,
                ),
                markers: Set<Marker>.of(controller.markers.values),
                myLocationEnabled: true,
                myLocationButtonEnabled: true,
                mapType: MapType.normal,
              ),
            ),
          ),
          Obx(
            () => SlidingUpPanel(
              controller: controller.panelController,
              // color: AppColors.white,
              minHeight: MediaQuery.of(context).size.height / 10,
              maxHeight: MediaQuery.of(context).size.height / 2,
              onPanelSlide: (position) {
                controller.changeMapHeight(position);
              },

              panel: Center(
                child: AnimatedSwitcher(
                  duration: Duration(milliseconds: 400),
                  child: controller.expandDetail.value
                      ? DetailLocationWidget(
                      merchantLocation:
                      controller.locationMarkerClick.value,
                      promoDetail: controller.promotionsMarkerClick,
                      onDismiss: () {
                        controller.changeExpandDetail(false);
                      }
                  )
                      : MerchantListNearMeWidget(
                    onDismiss: () {
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (controller.panelController.isAttached) {
                          controller.panelController.close();
                        } else {
                          print("Panel chưa ready để close.");
                        }
                      });
                      // controller.panelController.close();
                    },
                    promoNearMc: controller.promoNearMc.value,
                    callback: (mcLocation, promos) {
                      controller.changeExpandDetail(true);
                      controller.updateCameraPosition(LatLng(mcLocation.location!.y, mcLocation.location!.x));
                      controller.handleMarkerTap(mcLocation, promos);
                    },
                  ),
                ),
              ),
              collapsed: Container(
                decoration: BoxDecoration(
                    color: Colors.blueGrey,
                    // borderRadius: BorderRadius.all(Radius.circular(15))
                ),
                child: Column(
                  children: [
                    SizedBox(height: 12),
                    Container(
                      width: 40,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    Expanded(
                      child: Center(
                        child: Obx(
                          () => Text(
                            controller.expandDetail.value ? '${controller.locationMarkerClick.value.branchName}' : 'Xem khuyến mãi gần tôi',
                            style: style_S18_W600_WhiteColor,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // body: GoogleMap(
              //   onMapCreated: controller.onCompleteMapCreated,
              //   initialCameraPosition:
              //       CameraPosition(target: LatLng(21.0278, 105.8342), zoom: 11),
              //   markers: Set<Marker>.of(controller.markers.values),
              //   myLocationEnabled: true,
              //   myLocationButtonEnabled: true,
              // ),
            ),
          ),
          Container(
            height: 70,
            margin: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 20, left: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                TouchableWidget(
                  padding: EdgeInsets.zero,
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    color: AppColors.lightGreyText,
                    borderRadius: BorderRadius.all(Radius.circular(20)),
                  ),
                  onPressed: () {
                    Get.back();
                  },
                  child: Center(
                      child: Icon(
                    Icons.close,
                    color: AppColors.white,
                    size: 20,
                  )),
                ),
                SizedBox(
                  width: 5,
                ),
                Expanded(
                  child: Container(
                    child: Column(
                      children: [
                        Container(
                          height: 30,
                          child: Obx(
                                () => ListView.builder(
                                  padding: EdgeInsets.zero,
                                  scrollDirection: Axis.horizontal,
                                  itemCount: controller.merchantsCategory.length,
                                  itemBuilder: (context, index) {
                                    bool isSelected =
                                        controller.selectedIndexCategory.value == index;
                                    return TouchableWidget(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        controller.selectCategory(index);
                                      },
                                      child: Obx(
                                            () => Container(
                                          height: 25,
                                          padding: EdgeInsets.symmetric(horizontal: 15),
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.symmetric(horizontal: 3),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(15),
                                            border:
                                            Border.all(color: Colors.grey, width: 0.5),
                                            color: isSelected
                                                ? AppColors.tabSelected.withOpacity(0.5)
                                                : AppColors.white.withOpacity(0.8),
                                          ),
                                          child: Text(
                                              '${controller.merchantsCategory.value[index].name_vi}',
                                              style: TextStyle(
                                                  color: isSelected
                                                      ? AppColors.blackText
                                                      : AppColors.blackText,
                                                  fontWeight: FontWeight.w600,
                                                  fontFamily: kFontFamilyBeVietnamPro,
                                                  fontSize: isSelected ? 12 : 12)),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          ),
                        ),
                        SizedBox(height: 5,),
                        Container(
                          height: 30,
                          child: Obx(
                                () => ListView.builder(
                                  padding: EdgeInsets.zero,
                                  scrollDirection: Axis.horizontal,
                                  itemCount: controller.distanceKmOptions.length,
                                  itemBuilder: (context, index) {
                                    bool isSelected =
                                        controller.selectedIndexDistance.value == index;
                                    return TouchableWidget(
                                      padding: EdgeInsets.zero,
                                      onPressed: () {
                                        controller.selectDistance(index);
                                      },
                                      child: Obx(
                                            () => Container(
                                          height: 25,
                                          padding: EdgeInsets.symmetric(horizontal: 15),
                                          alignment: Alignment.center,
                                          margin: EdgeInsets.symmetric(horizontal: 3),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(15),
                                            border:
                                            Border.all(color: Colors.grey, width: 0.5),
                                            color: isSelected
                                                ? AppColors.tabSelected.withOpacity(0.5)
                                                : AppColors.white.withOpacity(0.8),
                                          ),
                                          child: Text(
                                              '${controller.distanceKmOptions.value[index]} Km',
                                              style: TextStyle(
                                                  color: isSelected
                                                      ? AppColors.blackText
                                                      : AppColors.blackText,
                                                  fontWeight: FontWeight.w600,
                                                  fontFamily: kFontFamilyBeVietnamPro,
                                                  fontSize: isSelected ? 12 : 12)),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
