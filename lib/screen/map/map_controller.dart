import 'dart:async';
import 'dart:convert';

import 'package:card_promo/data/model/promo_nearby_merchant.dart';
import 'package:card_promo/data/provider/location_service.dart';
import 'package:card_promo/util/api_constant.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/ufo_logger.dart';
import 'package:card_promo/widget/base_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart';
import 'package:sliding_up_panel/sliding_up_panel.dart';

import '../../app_controller.dart';
import '../../data/model/McCategory.dart';
import '../../data/model/login_model.dart';
import '../../data/model/merchant.dart';
import '../../data/model/promo_detail.dart';
import '../../data/model/user.dart';

import '../../data/provider/base_service.dart';
import '../../util/app_utils.dart';
import '../../widget/custom_marker.dart';

class MapBinding extends Bindings{
  @override
  void dependencies() {
    Get.lazyPut(()=> MapController(), fenix: false);
  }
}

class MapController extends GetxController with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  // RxList<Marker> markers = <Marker>[].obs;
  RxMap<MarkerId, Marker> markers = <MarkerId, Marker>{}.obs;

  List<Map<String, dynamic>> markersWithPromotions = [];

  String APIKEY = 'AIzaSyAYBQGBa7FwvdaqVFjM-t0PHc4wBYJnDeI';

  Completer<GoogleMapController> mapController = Completer<GoogleMapController>();
  LocationData? locationData;

  RxBool isLoadingSetup = true.obs;
  RxBool expandDetail = false.obs;
  Rx<MerchantLocation> locationMarkerClick = MerchantLocation().obs;
  RxList<PromoDetail> promotionsMarkerClick = <PromoDetail>[].obs;

  RxDouble mapHeight = 0.0.obs;

  RxInt selectedIndexCategory = 0.obs;
  RxList<McCategory> merchantsCategory = <McCategory>[McCategory(key: '',name_vi: 'Tất cả', name_en: 'All')].obs;
  RxList<PromoNearbyMerchant> promoNearMc = <PromoNearbyMerchant>[].obs;

  RxList<int> distanceKmOptions = [10, 25, 50, 100].obs;
  RxInt selectedIndexDistance = 0.obs;

  PanelController panelController = PanelController();

  void changeExpandDetail(bool value) {
    print('changeExpandDetail: $value');
    expandDetail.value = false;
  }

  void changeMapHeight(double position) {
    final screenHeight = MediaQuery.of(context).size.height;
    final minHeight = screenHeight * 0.5;
    final maxHeight = screenHeight * 0.9;
    mapHeight.value = maxHeight - (position * (maxHeight - minHeight));
  }

  @override
  void onInit() {
    super.onInit();
    merchantsCategory.value.addAll(_appController.config.value.merchantsCategory ?? []);
  }

  @override
  void onReady() {
    super.onReady();
    changeMapHeight(0.0);
    getLocation();
  }

  void fetchMerchantNear() async {
    String category = '';
    if ((selectedIndexCategory.value != 0)) {
      category = merchantsCategory[selectedIndexCategory.value].key ?? '';
    }
    int distance = 5;
    if (selectedIndexDistance.value != 0) {
      distance = distanceKmOptions[selectedIndexDistance.value];
    }

    var map = {
      "merchantCategory": category,
      "latitude": locationData?.latitude ?? '20.995068981300296',
      "longitude": locationData?.longitude ?? '105.86198855910659',
      "maxDistanceKm": distance // 1750.0
    };

    _appController.showLoading();
    var response = await BaseService().sendData(ApiConstant.urlGetMerchantNearbyPromo, jsonEncode(map));
    _appController.hideLoading();

    if (response.errorResponse.code == 200 && response.responseData.isNotEmpty) {
      var jsonMap = jsonDecode(response.responseData);
      if (jsonMap['result'] != null) {
        promoNearMc.value = (jsonMap['result'] as List<dynamic>)
            .map((item) => PromoNearbyMerchant.fromJson(item as Map<String, dynamic>))
            .toList();
        for (var merchant in promoNearMc.value) {
          for (var location in merchant.nearbyLocations) {
            markersWithPromotions.add({
              'location': location, // Thông tin vị trí của marker
              'promotions': merchant.promotions, // Danh sách khuyến mãi của merchant đó
              'merchantName': merchant.merchantName, // Thêm thông tin merchant để hiển thị
            });
          }
        }
        Set<Marker> markersLocal = {};

        for (var item in markersWithPromotions) {
          MerchantLocation location = item['location'];
          List<PromoDetail> promotions = item['promotions'];
          String merchantName = item['merchantName'];

          final customIcon = await createMarkerImage('${location.branchName}');

          markersLocal.add(
            Marker(
              markerId: MarkerId(location.branchName!), // ID duy nhất
              position: LatLng(location.location!.y, location.location!.x), // Vĩ độ, kinh độ
              icon: customIcon,
              infoWindow: InfoWindow(
                title: merchantName,
                snippet: '${promotions.length} khuyến mãi',
              ),
              anchor: Offset(0.5, 0.5), // anchor bottom center
              onTap: () {
                handleMarkerTap(location, promotions);
              },
            ),
          );
        }
          WidgetsBinding.instance.addPostFrameCallback((_) {
            markers.value = {for (var marker in markersLocal) marker.markerId: marker};
            promoNearMc.refresh();
          });
      }
    } else {
      AppUtils.showDialogAlert(
        context,
        type: BaseDialog.TYPE_ERROR,
        title: 'Có lỗi xảy ra, vui lòng kiểm tra kết nối mạng và thử lại.',
        description: '${response.errorResponse.message}',
      );
    }
  }

  void handleMarkerTap(MerchantLocation mcLocation, List<PromoDetail> promos) {
    expandDetail.value = true;
    locationMarkerClick.value = mcLocation;
    promotionsMarkerClick.clear();
    promotionsMarkerClick.addAll(promos);
  }

  Future<void> updateCameraPosition(LatLng latlng) async {
    final GoogleMapController controller = await mapController.future;
    controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: latlng,
          zoom: 14,
        ),
      ),
    );
  }

  void getLocation() async {
    locationData = await LocationService().getCurrentLocation();
    if (locationData == null) {
      AppUtils.showDialogAlert(
        context,
        type: BaseDialog.TYPE_ERROR,
        title: 'Không thể lấy được vị trị của bạn.',
        description: '',
      );
    }else {
      updateCameraPosition(LatLng(locationData!.latitude!, locationData!.longitude!),);
    }
    fetchMerchantNear();
  }

  void onCompleteMapCreated(GoogleMapController ggMapController) {
    mapController.complete(ggMapController);
    isLoadingSetup.value = false;
  }

  void selectCategory(int index) {
    selectedIndexCategory.value = index;
    merchantsCategory.refresh();
    promoNearMc.clear();
    promoNearMc.refresh();
    markers.clear();
    markers.refresh();
    fetchMerchantNear();
  }

  void selectDistance(int index) {
    selectedIndexDistance.value = index;
    distanceKmOptions.refresh();
    promoNearMc.clear();
    promoNearMc.refresh();
    markers.clear();
    markers.refresh();
    fetchMerchantNear();
  }

}