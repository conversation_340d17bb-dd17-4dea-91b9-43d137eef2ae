// import 'package:card_promo/widget/touchable_widget.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/src/widgets/framework.dart';
// import 'package:get/get.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
//
// import '../../app_controller.dart';
// import '../../loading_widget.dart';
// import '../../res/color/app_colors.dart';
// import '../../widget/MerchantListNearMeWidget.dart';
// import '../../widget/detail_location_widget.dart';
// import 'map_controller.dart';
//
// class MapScreen extends GetView<MapController> {
//   final MyAppController _appController = Get.find<MyAppController>();
//
//   @override
//   Widget build(BuildContext context) {
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       controller.context = context;
//     });
//     return Scaffold(
//       backgroundColor: AppColors.white,
//       body: Stack(
//         children: [
//           Obx(() => controller.isLoadingSetup.value ? LoadingWidget() : SizedBox.shrink()),
//           Obx(
//             () => Column(
//               children: [
//                 Expanded(
//                   child: GoogleMap(
//                     onMapCreated: (GoogleMapController ggMapController) {
//                       controller.onCompleteMapCreated(ggMapController);
//                     },
//                     initialCameraPosition: CameraPosition(
//                       target: LatLng(21.0278, 105.8342), // Mặc định: Hà Nội
//                       zoom: 11,
//                     ),
//                     markers: Set<Marker>.of(controller.markers.values),
//                     myLocationEnabled: true,
//                     myLocationButtonEnabled: true,
//                     mapType: MapType.normal,
//                   ),
//                 ),
//
//                 AnimatedSwitcher(
//                   duration: Duration(milliseconds: 400),
//                   child: controller.expandDetail.value
//                       ? DetailLocationWidget(
//                     merchantLocation: controller.locationMarkerClick.value,
//                     promoDetail: controller.promotionsMarkerClick,
//                     onDismiss: () => controller.expandDetail.value = false,
//                   )
//                       : MerchantListNearMeWidget(callback: () {
//
//                   },),
//                 )
//               ],
//             ),
//           ),
//           TouchableWidget(
//             padding: EdgeInsets.zero,
//             width: 35,
//             height: 35,
//             margin: EdgeInsets.only(
//                 top: MediaQuery.of(context).padding.top + 20, left: 20),
//             // padding: EdgeInsets.all(5),
//             decoration: BoxDecoration(
//               color: AppColors.lightGreyText,
//               borderRadius: BorderRadius.all(Radius.circular(20)),
//             ),
//             onPressed: () {
//               Get.back();
//             },
//             child: Center(
//                 child: Icon(
//                   Icons.close,
//                   color: AppColors.white,
//                   size: 20,
//                 )),
//           ),
//           Obx(() => controller.isLoadingSetup.value ? LoadingWidget() : SizedBox.shrink())
//         ],
//       ),
//     );
//   }
//
//   Widget _buildDetailLocation(BuildContext context) {
//     return Container(
//       height: MediaQuery.of(context).size.height/3,
//       child: SingleChildScrollView(
//         child: Container(
//           child: Column(
//             children: [
//
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }
