import 'dart:convert';

import 'package:card_promo/data/model/bank_card.dart';
import 'package:card_promo/data/provider/base_service.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';

import '../../app_controller.dart';


import '../../res/color/app_colors.dart';
import '../../util/api_constant.dart';
import '../../util/styles.dart';
import '../../widget/common_text_field.dart';
import '../../widget/scrolling_text.dart';
import '../../widget/touchable_widget.dart';

class RegistCardBindings extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => RegistCardController(), fenix: false);
  }
}

class RegistCardController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  RxList<Bank> banks = <Bank>[].obs;
  var selectedBank = ''.obs;
  var selectedCard = ''.obs;
  var userPhone = ''.obs;
  var userEmail = ''.obs;
  var userNote = ''.obs;

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();
    fetchBankCards();
  }

  void fetchBankCards() async {
    _appController.showLoading();
    var response = await BaseService()
        .sendData(ApiConstant.urlGetAllBankCard, jsonEncode(''));
    _appController.hideLoading();

    if (response.errorResponse.code == 200 &&
        response.responseData.isNotEmpty) {
      var jsonMap = jsonDecode(response.responseData);
      if (jsonMap['result'] != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // banks.value = jsonMap['result'].map((e) => Bank.fromJson(e)).toList();

          banks.value =
              jsonMap['result'].map<Bank>((e) => Bank.fromJson(e)).toList();
        });
      }
    }
  }

  void buildWidgetRegistCard() {
    RxString selectedReistCard = ''.obs;
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom,
            ),
            child: SingleChildScrollView(
              child: Container(
                  // height: MediaQuery.of(context).size.height / (3 / 2),
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: const BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.only(
                          topLeft: const Radius.circular(10),
                          topRight: const Radius.circular(10))),
                  child: Column(
                    children: [
                      CommonTextField(
                        // height: 60,
                        fontFamily: kFontFamilyBeVietnamPro,
                        controller: TextEditingController(text: userPhone.value),
                        hintText: "Số điện thoại",
                        hintTextFontSize: 16,
                        maxLength: 15,
                        keyboardType: TextInputType.phone,
                        isBorder: false,
                        onChanged: (value) => userPhone.value = value,
                      ),

                      CommonTextField(
                        // height: 60,
                        fontFamily: kFontFamilyBeVietnamPro,
                        controller: TextEditingController(text: userEmail.value),
                        hintText: "Email",
                        hintTextFontSize: 16,
                        keyboardType: TextInputType.emailAddress,
                        isBorder: false,
                        onChanged: (value) => userEmail.value = value,
                      ),

                      Obx(
                        () => DropdownButton<String>(
                          isExpanded: true,
                          value: selectedReistCard.value.isEmpty ? null : selectedReistCard.value,
                          hint: Text('${selectedReistCard.value.isEmpty ? "Chọn loại thẻ" : selectedReistCard.value}'),
                          items: banks
                              .firstWhereOrNull((bank) => bank.bankName == selectedBank.value)
                              ?.cards
                              .map((card) {
                            return DropdownMenuItem(
                              value: card.cardName,
                              child: ScrollingText(text: "${card.cardName}"),
                            );
                          }).toList() ??
                              [],
                          onChanged: (value) {
                            selectedReistCard.value = value ?? "";
                          },
                        ),
                      ),

                      CommonTextField(
                        fontFamily: kFontFamilyBeVietnamPro,
                        controller: TextEditingController(text: userNote.value),
                        hintText: "Ghi chú",
                        hintTextFontSize: 16,
                        maxLength: 200,
                        isBorder: false,
                        onChanged: (value) => userNote.value = value,
                      ),

                      SizedBox(height: 20),
                      TouchableWidget(
                          margin: EdgeInsets.symmetric(horizontal: 5),
                          decoration: BoxDecoration(
                              color: AppColors.main_background,
                              borderRadius:
                              BorderRadius.circular(10)),
                          onPressed: () {
                            submitRequest(selectedReistCard.value);
                          },
                          child: Text(
                            'Gửi yêu cầu',
                            style: style_S16_W600_WhiteColor,
                          ))
                    ],
                  )),
            ),
          );
        });
  }

  void submitRequest(String selectedReistCard) async {
    if (selectedBank.value.isEmpty) {
      Get.snackbar("Lỗi", "Vui lòng chọn ngân hàng",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
      return;
    }

    if (selectedReistCard.isEmpty) {
      Get.snackbar("Lỗi", "Vui lòng chọn loại thẻ",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
      return;
    }

    if (userPhone.value.isEmpty && userEmail.value.isEmpty) {
      Get.snackbar("Lỗi", "Vui lòng nhập thông tin liên hệ",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
      return;
    }

    if (userEmail.value.isNotEmpty &&
        checkEmptyAndValidEmail(userEmail.value)) {
      Get.snackbar("Lỗi", "Email sai định dạng",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
      return;
    }
    if (userPhone.value.isNotEmpty && checkInValidPhone(userPhone.value)) {
      Get.snackbar("Lỗi", "Số điện thoại sai định dạng",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white);
      return;
    }

    print("Ngân hàng: ${selectedBank.value}");
    print("Loại thẻ: ${selectedReistCard}");
    print("SĐT: ${userPhone.value}");
    print("Email: ${userEmail.value}");
    print("Ghi chú: ${userNote.value}");

    Map params = {
      "phone": userPhone.value,
      "email": userEmail.value,
      "note": userNote.value,
      "bankName": selectedBank.value,
      "cardName": selectedReistCard,
      "status": false
    };

    _appController.showLoading();
    var response = await BaseService()
        .sendData(ApiConstant.urlOpenCard, jsonEncode(params));
    _appController.hideLoading();

    if (response.errorResponse.code == 200 &&
        response.responseData.isNotEmpty) {
      var jsonMap = jsonDecode(response.responseData);
      if (jsonMap['result'] != null) {
        Get.snackbar("Thành công", "Đã nhận yêu cầu mở thẻ, bộ phận hỗ trợ sẽ liên hệ lại!",
            snackPosition: SnackPosition.BOTTOM, backgroundColor: AppColors.success, colorText: Colors.white);

      }
    }
  }
}
