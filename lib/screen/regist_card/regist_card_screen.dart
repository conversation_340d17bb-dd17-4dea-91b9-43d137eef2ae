import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/screen/regist_card/regist_card_controller.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../util/styles.dart';
import '../../widget/common_text_field.dart';
import '../../widget/scrolling_text.dart';

class RegistCardScreen  extends GetView<RegistCardController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Scaffold(
        backgroundColor: AppColors.main_background,
        body: Stack(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              child: Container(child: SvgPicture.asset(AppImages.ic_header)),
            ),

            Column(
              children: [
                Container(
                  margin: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 10,
                      left: 10,
                      bottom: 10,
                      right: 10),
                  alignment: Alignment.centerLeft,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TouchableWidget(
                          padding: EdgeInsets.all(5),
                          onPressed: () {
                            Get.back();
                          },
                          child: Icon(
                            Icons.close,
                            color: AppColors.white,
                          )),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        child: Text(
                          'Mở thẻ',
                          style: style_S18_W600_WhiteColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(20),
                            topRight: Radius.circular(20)),
                        color: AppColors.background_white),
                    alignment: Alignment.center,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(child: SingleChildScrollView(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text("Ngân hàng", style: style_S16_W600_BlackColor),
                                          Obx(() => DropdownButton<String>(
                                            isExpanded: true,
                                            value: controller.selectedBank.value.isEmpty ? null : controller.selectedBank.value,
                                            hint: Text("Chọn ngân hàng"),
                                            items: controller.banks.map((bank) {
                                              return DropdownMenuItem(
                                                value: bank.bankName,
                                                child: Text(bank.bankName, overflow: TextOverflow.ellipsis,),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              controller.selectedBank.value = value ?? "";
                                              controller.selectedCard.value = "";
                                            },
                                          )),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 10,),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text("Loại thẻ", style: style_S16_W600_BlackColor),
                                          Obx(() => DropdownButton<String>(
                                            isExpanded: true,
                                            value: controller.selectedCard.value.isEmpty ? null : controller.selectedCard.value,
                                            hint: Text("Chọn loại thẻ"),
                                            items: controller.banks
                                                .firstWhereOrNull((bank) => bank.bankName == controller.selectedBank.value)
                                                ?.cards
                                                .map((card) {
                                              return DropdownMenuItem(
                                                value: card.cardName,
                                                child: ScrollingText(text: "${card.cardName}"),
                                              );
                                            }).toList() ??
                                                [],
                                            onChanged: (value) {
                                              controller.selectedCard.value = value ?? "";
                                            },
                                          )),
                                        ],
                                      ),
                                    )
                                  ],
                                ),
                              ),




                              SizedBox(height: 10),
                              Obx(() {
                                var selectedBank = controller.banks.firstWhereOrNull((bank) => bank.bankName == controller.selectedBank.value);
                                var selectedCard = selectedBank?.cards.firstWhereOrNull((card) => card.cardName == controller.selectedCard.value);

                                if (selectedCard == null) return Container();
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Điều kiện:", style: style_S14_W600_BlackColor),
                                        SizedBox(height: 10,),
                                        Text("${selectedCard.required}", style: style_S14_W400_BlackColor),
                                      ],
                                    ),
                                    SizedBox(height: 10,),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Ưu đãi:", style: style_S14_W600_BlackColor),
                                        SizedBox(height: 10,),
                                        Text("${selectedCard.promotion}", style: style_S14_W400_BlackColor),
                                      ],
                                    ),
                                    SizedBox(height: 10,),
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text("Thông tin thêm:", style: style_S14_W600_BlackColor),
                                        SizedBox(height: 10,),
                                        Text("${selectedCard.fee}", style: style_S14_W400_BlackColor),
                                      ],
                                    ),
                                  ],
                                );
                              }),
                            ],
                          ),
                        )),
                        _inputFormRegist()
                      ],
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  _inputFormRegist() {
    return Container(
      padding: EdgeInsets.all(10),
      // decoration: BoxDecoration(
      //   border: Border.all(color: AppColors.grayBorder, width: 0.5),
      //     borderRadius: BorderRadius.only(
      //         topLeft: Radius.circular(20),
      //         topRight: Radius.circular(20)),
      //     color: AppColors.background_white),
      child: TouchableWidget(
          margin: EdgeInsets.symmetric(horizontal: 5),
          decoration: BoxDecoration(
              color: AppColors
                  .main_background,
              borderRadius:
              BorderRadius.circular(10)),
          onPressed: () {
            // controller.submitRequest();
            controller.buildWidgetRegistCard();
          },
          child: Text(
            'Yêu cầu mở thẻ',
            style: style_S16_W600_WhiteColor,
          )),
    );
  }
}
