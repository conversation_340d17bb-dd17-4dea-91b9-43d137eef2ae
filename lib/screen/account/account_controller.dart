import 'dart:convert';

import 'package:card_promo/data/model/user_info.dart';
import 'package:card_promo/data/provider/base_service.dart';
import 'package:card_promo/util/app_utils.dart';
import 'package:card_promo/util/local_storage.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

import '../../app_controller.dart';
import '../../util/api_constant.dart';
import '../../util/ufo_logger.dart';
import '../../widget/bottomsheet_setting_language.dart';

class AccountBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AccountController(), fenix: false);
  }
}

class AccountController extends GetxController
    with GetSingleTickerProviderStateMixin {
  final MyAppController _appController = Get.find<MyAppController>();

  late BuildContext context;

  @override
  void onInit() async {
    // TODO: implement onInit
    super.onInit();
  }

  @override
  void onReady() async {
    // TODO: implement onReady
    super.onReady();
    updateUserInfo();
  }

  Future<void> loginWithFacebook() async {
    try {
      final LoginResult result = await FacebookAuth.instance.login();
      if (result.status == LoginStatus.success) {
        final AccessToken accessToken = result.accessToken!;
        final userData = await FacebookAuth.instance.getUserData();

        // Lưu thông tin đăng nhập vào cache
        await _appController.saveLoginData(userData);
        _appController.user.refresh();
        updateUserInfo();
        Get.snackbar(
          'Thành công',
          'Đăng nhập Facebook thành công!',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Đăng nhập Facebook thất bại!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void logout() async {
    try {
      await FacebookAuth.instance.logOut();
      // Xóa thông tin đăng nhập khỏi cache
      await _appController.clearLoginData();

      Get.snackbar(
        'Thành công',
        'Đăng xuất thành công!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Đăng xuất thất bại!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> updateUserInfo() async {
    if ((_appController.user.value.userId != null &&
            _appController.user.value.userId!.isNotEmpty) &&
        (_appController.user.value.userName != null &&
            _appController.user.value.userName!.isNotEmpty)) {
      Map params = {
        'userId': _appController.user.value.userId,
        'userName': _appController.user.value.userName
      };
      _appController.showLoading();
      var response = await BaseService().sendData(ApiConstant.urlLogin, jsonEncode(params));
      _appController.hideLoading();
      if (response.errorResponse.code == 200 &&
          response.responseData.isNotEmpty) {
        UserInfo? user = AppUtils.parseResponse<UserInfo>(
            context, response.responseData, UserInfo.fromJson);
        if (user != null) {
          _appController.user.value.contributionPoints = user.contributionPoints;
          await LocalStorage().saveData(LocalStorage.KEY_CONTRIBUTION_POINT, _appController.user.value.contributionPoints ?? 0);
        }
      }
    }
  }

  void onPressLanguage() async {
    String currentLanguageApp = _appController.isEnglish.value ? 'en' : 'vi';
    if (currentLanguageApp.length > 0) {
      List<ObjSettingSelect> listLanguage = [
        ObjSettingSelect('Tiếng Việt', 'vi'),
        ObjSettingSelect('English', 'en'),
      ];

      showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
        ),
        builder: (context) => SettingLanguage(
          onSelectItem: (settingSelected){
            UfoLogger().writeLog('select item ${settingSelected.value}');
            handlerSelectLanguage(settingSelected.value);
          },
          listSetting: listLanguage,
          valueSelected: ObjSettingSelect('', currentLanguageApp),
        ),
        isScrollControlled: true,
        isDismissible: false,
      );
    }
  }

  void onPressAds() {
    AppUtils.launchMailHotroMpos(true);
  }

  void onPressReport() {
    AppUtils.launchMailHotroMpos(false);
  }

  void handlerSelectLanguage(String? languageSelected) {
    Get.back();
    Get.updateLocale(languageSelected == 'vi' ?Locale('vi', 'VN'):Locale('en', 'US') );
    LocalStorage().setLanguageApp(languageSelected);
    // NativeBridge.getInstance().nativeSetLanguage(languageSelected);
  }
}