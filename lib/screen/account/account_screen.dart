import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../res/string/app_strings.dart';
import '../../util/styles.dart';

class AccountScreen extends GetView<AccountController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: AppBar(
        backgroundColor: AppColors.main_background,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.close_rounded,
            color: AppColors.white,
            size: 24,
          ),
          onPressed: () => Get.back(),
        ),
        title: Text(
          AppStrings.getString(AppStrings.settings),
          style: style_S20_W600_WhiteColor,
        ),
        centerTitle: false,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
      ),
      body: Container(
        color: AppColors.grayBackground,
        child: Column(
          children: [
            SizedBox(height: 16),
            // Settings Card
            Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.lightGreyText.withOpacity(0.1),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Header Section
                    Container(
                      padding: EdgeInsets.all(24),
                      child: Row(
                        children: [
                          Container(
                            width: 56,
                            height: 56,
                            decoration: BoxDecoration(
                              color: AppColors.main_background.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Icon(
                              Icons.settings_rounded,
                              color: AppColors.main_background,
                              size: 28,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Cài đặt ứng dụng',
                                  style: style_S18_W600_BlackColor,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  'Tùy chỉnh theo sở thích của bạn',
                                  style: style_S14_W400_GreyColor,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Divider
                    Container(
                      height: 1,
                      margin: EdgeInsets.symmetric(horizontal: 24),
                      color: AppColors.grayBorder,
                    ),

                    // Settings List
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.symmetric(vertical: 8),
                        child: Column(
                          children: [
                            _buildSettingItem(
                              Icons.language_rounded,
                              AppStrings.getString(AppStrings.language),
                              'Thay đổi ngôn ngữ hiển thị',
                              () => controller.onPressLanguage(),
                            ),
                            _buildSettingItem(
                              Icons.phone_rounded,
                              AppStrings.getString(AppStrings.contactAds),
                              'Liên hệ quảng cáo và hỗ trợ',
                              () => controller.onPressAds(),
                            ),
                            _buildSettingItem(
                              Icons.feedback_rounded,
                              AppStrings.getString(AppStrings.feedback),
                              'Gửi phản hồi và báo lỗi',
                              () => controller.onPressReport(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(IconData icon, String title, String subtitle, Function callback) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => callback(),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.main_background.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: AppColors.main_background,
                  size: 24,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: style_S16_W600_BlackColor,
                    ),
                    SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: style_S14_W400_GreyColor,
                    ),
                  ],
                ),
              ),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: AppColors.grayBackground,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: AppColors.greyTextContent,
                  size: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Keep old method for backward compatibility if needed
  _buildRow(IconData icon, String title, Function callback) {
    return _buildSettingItem(icon, title, '', callback);
  }
}
