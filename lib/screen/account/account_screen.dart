import 'package:card_promo/screen/account/account_controller.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

import '../../app_controller.dart';
import '../../res/color/app_colors.dart';
import '../../res/image/app_images.dart';
import '../../res/string/app_strings.dart';
import '../../util/styles.dart';

class AccountScreen extends GetView<AccountController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return Scaffold(
      backgroundColor: AppColors.main_background,
      body: Stack(
        children: [
          Container(
            width: MediaQuery.of(context).size.width,
            child: Container(child: SvgPicture.asset(AppImages.ic_header)),
          ),
          Column(
            children: [
              Container(
                margin: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 10,
                    left: 10,
                    bottom: 10,
                    right: 10),
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    TouchableWidget(
                        padding: EdgeInsets.all(5),
                        onPressed: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.close,
                          color: AppColors.white,
                        )),
                    SizedBox(
                      width: 10,
                    ),
                    Expanded(
                      child: Text(
                        AppStrings.getString(AppStrings.settings),
                        style: style_S18_W600_WhiteColor,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                      color: AppColors.background_white),
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      // Container(
                      //   height: MediaQuery.of(context).size.height / 3,
                      //   padding: EdgeInsets.all(16),
                      //   child: Column(
                      //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      //     children: [
                      //       Obx(
                      //         () => CircleAvatar(
                      //           radius: 50,
                      //           backgroundImage: !isNullOrEmpty(
                      //                   _appController.user.value.userAvatar)
                      //               ? NetworkImage(
                      //                   _appController.user.value.userAvatar!)
                      //               : null,
                      //           backgroundColor: Colors.grey.shade300,
                      //           child: isNullOrEmpty(
                      //                   _appController.user.value.userAvatar)
                      //               ? const Icon(
                      //                   Icons.person,
                      //                   size: 50,
                      //                   color: Colors.white,
                      //                 )
                      //               : null,
                      //         ),
                      //       ),
                      //       SizedBox(height: 16),
                      //       Obx(() => _appController.isLoggedIn.value
                      //           ? Column(
                      //               children: [
                      //                 Text(
                      //                   '${_appController.user.value.userName}',
                      //                   style: TextStyle(
                      //                     fontSize: 20,
                      //                     fontWeight: FontWeight.bold,
                      //                   ),
                      //                 ),
                      //                 SizedBox(height: 8),
                      //                 (isNullEmptyFalseOrZero(_appController
                      //                         .user.value.contributionPoints))
                      //                     ? SizedBox.shrink()
                      //                     : Text(
                      //                         'Số lần đóng góp ${_appController.user.value..contributionPoints}',
                      //                         style: TextStyle(
                      //                           fontSize: 20,
                      //                           fontWeight: FontWeight.bold,
                      //                         ),
                      //                       ),
                      //                 SizedBox(height: 8),
                      //                 TouchableWidget(
                      //                   onPressed: () => controller.logout(),
                      //                   child: Text(
                      //                     AppStrings.getString(
                      //                         AppStrings.logout),
                      //                     style: TextStyle(
                      //                       color: Colors.red,
                      //                       fontSize: 16,
                      //                     ),
                      //                   ),
                      //                 ),
                      //               ],
                      //             )
                      //           : ElevatedButton.icon(
                      //               onPressed: () =>
                      //                   controller.loginWithFacebook(),
                      //               icon: Icon(Icons.facebook, color: AppColors.primary,),
                      //               label: Text(AppStrings.getString(
                      //                   AppStrings.loginWithFacebook)),
                      //               style: ElevatedButton.styleFrom(
                      //                 backgroundColor:
                      //                     AppColors.main_background_light,
                      //                 padding: EdgeInsets.symmetric(
                      //                     horizontal: 24, vertical: 12),
                      //               ),
                      //             )),
                      //     ],
                      //   ),
                      // ),
                      // Divider(thickness: 0.5),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                _buildRow(Icons.language,
                                    AppStrings.getString(AppStrings.language),
                                    () {
                                  controller.onPressLanguage();
                                }),
                                Divider(thickness: 0.5),
                                _buildRow(Icons.phone,
                                    AppStrings.getString(AppStrings.contactAds),
                                    () {
                                  controller.onPressAds();
                                }),
                                Divider(thickness: 0.5),
                                _buildRow(Icons.feedback,
                                    AppStrings.getString(AppStrings.feedback),
                                    () {
                                  controller.onPressReport();
                                }),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  _buildRow(IconData icon, String title, Function callback) {
    return TouchableWidget(
      padding: EdgeInsets.symmetric(vertical: 15),
      onPressed: () => callback(),
      child: Container(
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primary,
            ),
            SizedBox(
              width: 20,
            ),
            Text(
              '$title',
              style: TextStyle(
                  fontSize: 16,
                  color: AppColors.blackText,
                  fontWeight: FontWeight.w500,
                  fontFamily: kFontFamilyBeVietnamPro),
            ),
          ],
        ),
      ),
    );
  }
}
