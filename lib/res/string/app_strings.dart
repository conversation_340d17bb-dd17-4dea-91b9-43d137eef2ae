import 'package:get/get.dart';
import 'en_strings.dart';
import 'vi_strings.dart';

class AppStrings extends Translations {
  static final String localeCodeVi = 'vi_VN';
  static final String localeCodeEn = 'en_US';

  static final String language_code_vi = 'vi';

  @override
  Map<String, Map<String, String>> get keys => {
        localeCodeVi: viStrings,
        localeCodeEn: enStrings,
      };

  static String getString(String key) {
    String result = '';
    Map<String, String> selectedLanguage =
        Get.locale.toString() == localeCodeEn ? enStrings : viStrings;
    if (selectedLanguage != null &&
        selectedLanguage.containsKey(key) &&
        selectedLanguage[key] != null) {
      result = selectedLanguage[key] ?? '';
    }

    return result;
  }

  static String titleNotice = 'titleNotice';
  static String splashSlogan = 'splashSlogan';
  static String invalidEmail = 'invalidEmail';
  static String emptyEmail = 'emptyEmail';
  static String password = 'password';
  static String passwordStep2 = 'passwordStep2';
  static String email = 'email';
  static String timeoutError = 'timeoutError';
  static String errorUnknown = 'errorUnknown';
  static String ok = 'ok';
  static String cancel = 'cancel';
  static String sharePromoTitle = 'sharePromoTitle';
  static String contactAds = 'contactAds';
  static String language = 'language';
  static String feedback = 'feedback';
  static String loginWithFacebook = 'loginWithFacebook';
  static String logout = 'logout';
  static String settings = 'settings';
  static String tt_recently = 'tt_recently';
  static String tt_favourite = 'tt_favourite';
  static String tt_store = 'tt_store';
  static String tt_contribute = 'tt_contribute';
  static String tt_contribute_detail = 'tt_contribute_detail';
  static String tt_register_card = 'tt_register_card';
  static String tt_register_card_detail = 'tt_register_card_detail';
  static String tt_all = 'tt_all';
  static String tt_suggest = 'tt_suggest';
  static String tt_add_card = 'tt_add_card';
  static String tt_end_date = 'tt_end_date';
  static String tt_detail = 'tt_detail';
  static String tt_add_your_card = 'tt_add_your_card';
  static String tt_add = 'tt_add';
  static String tt_delete = 'tt_delete';
  static String tt_favourite_promo = 'tt_favourite_promo';
  static String tt_promo_detail = 'tt_promo_detail';
  static String tt_rule_promo = 'tt_rule_promo';
  static String tt_share_promo = 'tt_share_promo';
  static String tt_in_progess = 'tt_in_progess';
  static String tt_exprite_date = 'tt_exprite_date';
  static String tt_start_date_full = 'tt_start_date_full';
  static String tt_end_date_full = 'tt_end_date_full';
  static String tt_read_more = 'tt_read_more';
  static String tt_collapse = 'tt_collapse';
  static String tt_location_at = 'tt_location_at';
}