class AppImages {
  static const String _internalImagePath = 'lib/res/image/';
  static const String ic_dialog_error = _internalImagePath + 'ic_dialog_error.png';
  static const String ic_dialog_warning = _internalImagePath + 'ic_dialog_warning.png';
  static const String ic_dialog_success = _internalImagePath + 'ic_dialog_success.png';
  static const String ic_new_banner = _internalImagePath + 'new_banner.png';
  static const String ic_banner_podcast_into_app = _internalImagePath + 'ic_banner_podcast_into_app.png';
  static const String ic_header = _internalImagePath + 'ic_header.svg';
  static const String icClearText2 = _internalImagePath + 'ic_clear.png';
  static const String icEye = _internalImagePath + 'ic_eye.png';
  static const String ic_phone = _internalImagePath + 'ic_phone.svg';
  static const String ic_zalo = _internalImagePath + 'ic_zalo.svg';
  static const String ic_email = _internalImagePath + 'ic_email.svg';
  static const String ic_hotsale_1 = _internalImagePath + 'ic_hotsale_1.png';
  static const String ic_hotsale_2 = _internalImagePath + 'ic_hotsale_2.png';
  static const String ic_communmiti = _internalImagePath + 'ic_communiti.png';
  static const String ic_favourite = _internalImagePath + 'ic_favourite.png';
  static const String ic_group_card = _internalImagePath + 'ic_group_card.png';
  static const String background_share_wg = _internalImagePath + 'background_share_wg.png';
  static const String play_store_512 = _internalImagePath + 'play_store_512.png';
  static const String ic_selected = _internalImagePath + 'ic_selected.png';
  static const String ic_marker = _internalImagePath + 'marker.png';
  static const String ic_star = _internalImagePath + 'star.png';
  static const String ic_star_not_check = _internalImagePath + 'star_not_check.png';

  //demo remove when go live
  static const String ic_banner_demo = _internalImagePath + 'demo_banner_1.png';
  static const String ic_banner_demo1 = _internalImagePath + 'demo_banner_2.png';
  static const String ic_techcombank_demo = _internalImagePath + 'icon_tech_demo.png';


}
