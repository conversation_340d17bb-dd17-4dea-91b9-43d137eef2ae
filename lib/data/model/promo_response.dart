import 'paging.dart';
import 'promo_data.dart';
import 'promo_detail.dart';

class PromoResponse {
  final List<PromoDetail> promoData;
  final Paging paging;

  PromoResponse({
    required this.paging,
    required this.promoData,
  });
  factory PromoResponse.fromJson(Map<String, dynamic> json) => PromoResponse(
    paging: Paging.fromJson(json["paging"]),
    promoData: List<PromoDetail>.from(
      (json["promoData"] as List).map((x) => PromoDetail.fromJson(x)),
    ),
  );

  Map<String, dynamic> toJson() => {
    "promoData": List<dynamic>.from(promoData.map((x) => x.toJson())),
    "paging": paging.toJson(),
  };
}