/// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import 'dart:convert';

User userFromJson(String str) => User.fromJson(json.decode(str));

String userToJson(User data) => json.encode(data.toJson());

class User {
    User({
        required this.password,
        required this.userName,
    });

    String password;
    String userName;

    factory User.fromJson(Map<dynamic, dynamic> json) => User(
        password: json["password"] ?? '',
        userName: json["userName"] ?? '',
    );

    Map<dynamic, dynamic> toJson() => {
        "password": password,
        "userName": userName,
    };
}
