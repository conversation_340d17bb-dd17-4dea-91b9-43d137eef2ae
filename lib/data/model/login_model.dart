/// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import 'dart:convert';

LoginModel loginModelFromJson(String str) => LoginModel.fromJson(json.decode(str));

String loginModelToJson(LoginModel data) => json.encode(data.toJson());

class LoginModel {
    LoginModel({
        required this.uid,
        required this.cardDetails,
        required this.lastTimeLogin,
        required this.username,
        required this.token,
    });

    String uid;
    List<CardDetail> cardDetails;
    int lastTimeLogin;
    String username;
    String token;

    factory LoginModel.fromJson(Map<dynamic, dynamic> json) => LoginModel(
        uid: json["uid"] ?? '',
        cardDetails: List<CardDetail>.from(json["cardDetails"].map((x) => CardDetail.fromJson(x))),
        lastTimeLogin: json["lastTimeLogin"] ?? '',
        username: json["username"] ?? '',
        token: json["token"] ?? '',
    );

    Map<dynamic, dynamic> toJson() => {
        "uid": uid,
        "cardDetails": List<dynamic>.from(cardDetails.map((x) => x.toJson())),
        "lastTimeLogin": lastTimeLogin,
        "username": username,
        "token": token,
    };
}

class CardDetail {
    CardDetail({
        required this.bankCode,
        required this.bank,
        required this.cardHolderName,
        required this.cardType,
        required this.expireDate,
        required this.pan,
    });

    String bankCode;
    String bank;
    String cardHolderName;
    String cardType;
    String expireDate;
    String pan;

    factory CardDetail.fromJson(Map<dynamic, dynamic> json) => CardDetail(
        bankCode: json["bankCode"],
        bank: json["bank"],
        cardHolderName: json["cardHolderName"],
        cardType: json["cardType"],
        expireDate: json["expireDate"],
        pan: json["pan"],
    );

    Map<dynamic, dynamic> toJson() => {
        "bankCode": bankCode,
        "bank": bank,
        "cardHolderName": cardHolderName,
        "cardType": cardType,
        "expireDate": expireDate,
        "pan": pan,
    };
}
