import '../../res/string/app_strings.dart';

class ErrorResponse {
  late int code;
  late String message;

  ErrorResponse({this.code = 0, this.message = ""});

  ErrorResponse.paserData(Map<String, dynamic>? data){
    message = "";
    code = 0;
    if (data != null){
      Map<String, dynamic>? errorCode = data['error'];
      if (errorCode != null) {
        code = errorCode['code'];
        message = code != 0 ? "Mã $code - ${data['message']}" : data['message'];
      }else{
        code = data['code'] != null ? data['code'] : 0;
        message = code != 0 ? "Mã $code - ${data['message']}" : data['message'];
      }
      if (code == 200 || code == 1000){
        code = 1000;
        message = "DO_SERVICE_SUCCESS";
      }
    }else{
      message = AppStrings.getString('Không thể giải mã dữ liệu.');
    }
  }
}