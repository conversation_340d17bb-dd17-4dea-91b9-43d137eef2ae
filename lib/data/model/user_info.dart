class UserInfo {
  UserInfo({
    this.userId,
    this.userName,
    this.userAvatar,
    this.contributionPoints,
    this.createTime,
    this.updateTime,
  });

  String? userId;       // ID từ Facebook
  String? userName;
  String? userAvatar;
  int? contributionPoints; // Điểm đóng góp
  int? createTime;
  int? updateTime;

  clean() {
    userId = '';
    userName = '';
    userAvatar = '';
    contributionPoints = 0;
    createTime = 0;
    updateTime = 0;
  }

  factory UserInfo.fromJson(Map<dynamic, dynamic> json) => UserInfo(
    userId: json["userId"] ?? '',
    userName: json["userName"] ?? '',
    userAvatar: json["userAvatar"] ?? '',
    contributionPoints: json["contributionPoints"] ?? 0,
    createTime: json["createTime"] ?? 0,
    updateTime: json["updateTime"] ?? 0,
  );

  Map<dynamic, dynamic> toJson() => {
    "userId": userId,
    "userName": userName,
    "userAvatar": userAvatar,
    "contributionPoints": contributionPoints,
    "createTime": createTime,
    "updateTime": updateTime,
  };
}