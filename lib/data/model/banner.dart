class BannerData {
  final String urlBanner;
  final String urlLink;

  BannerData({
    required this.urlBanner,
    required this.urlLink,
  });

  factory BannerData.fromJson(Map<dynamic, dynamic> json) =>
      BannerData(
        urlBanner: json["urlBanner"] ?? '',
        urlLink: json["urlLink"] ?? '',
      );

  Map<dynamic, dynamic> toJson() =>
      {
        "urlBanner": urlBanner,
        "urlLink": urlLink,
      };
}