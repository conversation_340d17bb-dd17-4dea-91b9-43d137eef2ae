/// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import 'dart:convert';

import 'package:card_promo/data/model/promo_detail.dart';

import 'merchant.dart';

PromoNearbyMerchant promoNearbyMerchantFromJson(String str) => PromoNearbyMerchant.fromJson(json.decode(str));

String promoNearbyMerchantToJson(PromoNearbyMerchant data) => json.encode(data.toJson());

class PromoNearbyMerchant {
    PromoNearbyMerchant({
        required this.promotions,
        required this.merchantId,
        required this.phone,
        required this.distanceInMeters,
        required this.nearbyLocations,
        required this.email,
        required this.merchantName,
    });

    List<PromoDetail> promotions;
    String merchantId;
    String phone;
    double distanceInMeters;
    List<MerchantLocation> nearbyLocations;
    String email;
    String merchantName;

    factory PromoNearbyMerchant.fromJson(Map<dynamic, dynamic> json) => PromoNearbyMerchant(
        promotions: List<PromoDetail>.from(json["promotions"].map((x) => PromoDetail.fromJson(x))),
        merchantId: json["merchantId"] ?? '',
        phone: json["phone"] ?? '',
        distanceInMeters: json["distanceInMeters"] ?? 0.0,
        nearbyLocations: List<MerchantLocation>.from(json["nearbyLocations"].map((x) => MerchantLocation.fromJson(x))),
        email: json["email"] ?? '',
        merchantName: json["merchantName"] ?? '',
    );

    Map<dynamic, dynamic> toJson() => {
        "promotions": List<dynamic>.from(promotions.map((x) => x.toJson())),
        "merchantId": merchantId,
        "phone": phone,
        "distanceInMeters": distanceInMeters,
        "nearbyLocations": List<dynamic>.from(nearbyLocations.map((x) => x.toJson())),
        "email": email,
        "merchantName": merchantName,
    };
}
