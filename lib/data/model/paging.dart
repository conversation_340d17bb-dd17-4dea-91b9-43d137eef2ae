class Paging {
  final int pageIndex;
  final int pageSize;
  final int dataCount;

  Paging({
    required this.pageIndex,
    required this.pageSize,
    required this.dataCount,
  });

  factory Paging.fromJson(Map<String, dynamic> json) => Paging(
    pageIndex: json["pageIndex"],
    pageSize: json["pageSize"],
    dataCount: json["dataCount"],
  );

  Map<String, dynamic> toJson() => {
    "pageIndex": pageIndex,
    "pageSize": pageSize,
    "dataCount": dataCount,
  };
}
