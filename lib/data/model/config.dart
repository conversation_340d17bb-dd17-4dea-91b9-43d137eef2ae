/// YApi QuickType插件生成，具体参考文档:https://plugins.jetbrains.com/plugin/18847-yapi-quicktype/documentation

import 'dart:convert';

import 'McCategory.dart';
import 'bank_data.dart';

Config configFromJson(String str) => Config.fromJson(json.decode(str));

String configToJson(Config data) => json.encode(data.toJson());

class Config {
    Config({
        this.isShowAds,
        this.linkGroup,
        this.isReloadBank,
        this.isReloadBanner,
        this.banks,
        this.merchantsCategory,
    });

    String? isShowAds;
    String? linkGroup;
    String? isReloadBank;
    String? isReloadBanner;

    List<BankData>? banks;
    List<McCategory>? merchantsCategory;

    factory Config.fromJson(Map<dynamic, dynamic> json) => Config(
        isShowAds: json["isShowAds"] ?? '0',
        linkGroup: json["linkGroup"] ?? '',
        isReloadBank: json["isReloadBank"] ?? '0',
        isReloadBanner: json["isReloadBanner"] ?? '0',
        // banks: json["banks"].map((item) => BankData.fromJson(item as Map<String, dynamic>))
        //     .toList() ?? [],
        banks: (json["banks"] as List<dynamic>?)
            ?.map((item) => BankData.fromJson(item as Map<String, dynamic>))
            .toList(),
        // merchantsCategory: json["merchantsCategory"].map((item) => McCategory.fromJson(item as Map<String, dynamic>))
        //     .toList() ?? [],
        merchantsCategory: (json["merchantsCategory"] as List<dynamic>?)
            ?.map((item) => McCategory.fromJson(item as Map<String, dynamic>))
            .toList(),
    );

    Map<dynamic, dynamic> toJson() => {
        "isShowAds": isShowAds,
        "linkGroup": linkGroup,
        "isReloadBank": isReloadBank,
        "isReloadBanner": isReloadBanner,
        "banks": banks,
        "merchantsCategory": merchantsCategory
    };
}
