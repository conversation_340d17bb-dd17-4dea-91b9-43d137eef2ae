class BankCard {
  final String cardType; // Visa, Mastercard, JCB, ...
  final String cardName; // Visa Platinum, Mastercard Gold, ...
  final String promotion; // Ưu đãi khi mở thẻ
  final String fee; // phí
  final String required; // yêu cầu

  BankCard({
    required this.cardType,
    required this.cardName,
    required this.promotion,
    required this.fee,
    required this.required,
  });

  factory BankCard.fromJson(Map<String, dynamic> json) {
    return BankCard(
      cardType: json['cardType'] ?? '',
      cardName: json['cardName'] ?? '',
      promotion: json['promotion'] ?? '',
      fee: json['fee'] ?? '',
      required: json['required'] ?? '',
    );
  }
}

class Bank {
  final String bankName;
  final String bankCode;
  final List<BankCard> cards;

  Bank({
    required this.bankName,
    required this.bankCode,
    required this.cards,
  });

  factory Bank.fromJson(Map<String, dynamic> json) {
    var cardList = json['cards'] as List;
    List<BankCard> cards = cardList.map((c) => BankCard.fromJson(c)).toList();

    return Bank(
      bankName: json['bankName'] ?? '',
      bankCode: json['bankCode'],
      cards: cards,
    );
  }
}
