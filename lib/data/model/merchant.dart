import 'dart:convert';

class Merchant {
  String id;
  String name;
  String email;
  String phone;
  List<MerchantLocation> locations;

  Merchant({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.locations,
  });

  factory Merchant.fromJson(Map<String, dynamic> json) {
    return Merchant(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      locations: (json['locations'] as List<dynamic>?)
          ?.map((loc) => MerchantLocation.fromJson(loc))
          .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'locations': locations.map((loc) => loc.toJson()).toList(),
    };
  }
}

class MerchantLocation {
  String? branchName;
  String? address;
  GeoJsonPoint? location;

  MerchantLocation({
    this.branchName,
    this.address,
    this.location,
  });

  factory MerchantLocation.from<PERSON>son(Map<String, dynamic> json) {
    return MerchantLocation(
      branchName: json['branchName'] ?? '',
      address: json['address'] ?? '',
      location: json['location'] != null ? GeoJsonPoint.fromJson(json['location']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'branchName': branchName,
      'address': address,
      'location': location?.toJson(),
    };
  }
}

class GeoJsonPoint {
  double x;
  double y;
  String type;
  List<double> coordinates;

  GeoJsonPoint({
    required this.x,
    required this.y,
    required this.type,
    required this.coordinates,
  });

  factory GeoJsonPoint.fromJson(Map<String, dynamic> json) {
    return GeoJsonPoint(
      x: (json['x'] ?? 0).toDouble(),
      y: (json['y'] ?? 0).toDouble(),
      type: json['type'] ?? 'Point',
      coordinates: (json['coordinates'] as List<dynamic>?)
          ?.map((coord) => (coord as num).toDouble())
          .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'x': x,
      'y': y,
      'type': type,
      'coordinates': coordinates,
    };
  }
}
