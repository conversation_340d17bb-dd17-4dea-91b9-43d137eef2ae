import 'package:intl/intl.dart';

class PromoData {
  final String id;
  final String title;
  final String description;
  final String bankCode;
  final String bankName;
  final String iconBank;
  final List<String> scheme;
  final String category; // lĩnh v<PERSON><PERSON> <PERSON><PERSON>ến mãi
  final bool active;
  final int startDate; // lưu dưới dạng epoch milliseconds
  final int endDate;

  PromoData({
    required this.id,
    required this.title,
    required this.description,
    required this.bankCode,
    required this.bankName,
    required this.iconBank,
    required this.scheme,
    required this.category,
    required this.active,
    required this.startDate,
    required this.endDate,
  });

  factory PromoData.fromJson(Map<dynamic, dynamic> json) => PromoData(
    id: json["id"] ?? '',
    title: json["title"] ?? '',
    description: json["description"] ?? '',
    bankCode: json["bankCode"] ?? '',
    bankName: json["bankName"] ?? '',
    iconBank: json["iconBank"] ?? '',
    scheme: List<String>.from(json["scheme"].map((x) => x.toString())),
    category: json["category"] ?? '',
    active: json["active"] ?? true,
    startDate: json["startDate"] ?? 0,
    endDate: json["endDate"] ?? 0,
  );

  Map<dynamic, dynamic> toJson() => {
    "id": id,
    "title": title,
    "description": description,
    "bankCode": bankCode,
    "bankName": bankName,
    "iconBank": iconBank,
    "scheme": List<dynamic>.from(scheme.map((x) => x)),
    "category": category,
    "active": active,
    "startDate": startDate,
    "endDate": endDate,
  };

  String? get startDateAsDate {
    if (startDate == 0) return null;
    final dt = DateTime.fromMillisecondsSinceEpoch(startDate);
    // return DateTime(dt.year, dt.month, dt.day);
    return DateFormat('yyyy-MM-dd').format(DateTime(dt.year, dt.month, dt.day));
  }

  String? get endDateAsDate {
    if (endDate == 0) return null;
    final dt = DateTime.fromMillisecondsSinceEpoch(endDate);
    return DateFormat('yyyy-MM-dd').format(DateTime(dt.year, dt.month, dt.day));
  }
}
