class BankData {
  final String? bankName;
  final String? bankCode;
  final String? shortName;
  final String? icon;

  BankData({
    this.bankName,
    this.bankCode,
    this.shortName,
    this.icon,
  });

  factory BankData.fromJson(Map<dynamic, dynamic> json) =>
      BankData(
        bankName: json["bankName"] ?? '',
        bankCode: json["bankCode"] ?? '',
        shortName: json["shortName"] ?? '',
        icon: json["iconUrl"] ?? '',
      );

  Map<dynamic, dynamic> toJson() =>
      {
        "bankName": bankName,
        "bankCode": bankCode,
        "shortName": shortName,
        "iconUrl": icon,
      };
}