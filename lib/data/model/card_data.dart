class CardData {
  String? name;
  String? scheme;
  String? pan; // 6 số đầu
  String? icon;

  CardData({
    this.name,
    this.scheme,
    this.pan,
    this.icon,
  });

  factory CardData.fromJson(Map<dynamic, dynamic> json) =>
      CardData(
        name: json["name"] ?? '',
        scheme: json["scheme"] ?? '',
        pan: json["pan"] ?? '',
        icon: json["icon"] ?? '',
      );

  Map<dynamic, dynamic> toJson() =>
      {
        "name": name,
        "scheme": scheme,
        "pan": pan,
        "icon": icon,
      };
}