import 'package:intl/intl.dart';
class PromoDetail {
   String? id;
  // Thông tin thẻ
   String? bankCode; // bank code
   String? bankName; // tên bank
   String? iconBank; // icon bank
   List<String>? scheme; // visa/master/napas/ ...
   List<int>? bins; // ds đầu bin được hỗ trợ
  // Thông tin promo
   String? title; // tên km
   List<String>? category; // lĩnh vực khuyến mãi
   String? description; // chi tiết km
   int? budget; // Ngân sách cho km
   int? startDate; // Ngày bắt đầu và kết thúc khuyến mãi
   int? endDate;
   String? promoImageUrl; // URL hình ảnh chính của khuyến mãi
   String? promoCode; // (Tùy chọn) Mã khuyến mãi dùng cho việc áp dụng giảm giá
   String? linkPromo; // trang chính của khuyến mãi
   List<PromoOptionAmountReduce>? promoOptionAmountReduce;
   List<PromoOptionAmountRefund>? promoOptionAmountRefund;
   String? merchantId; // tên mc áp dụng
   String? merchantName; // tên mc áp dụng
   String? storeLocation; // địa chỉ store áp dụng
   String? termsAndConditions; // điều khoản và điều kiện sử dụng
   String? userNameContribution; // tên người đóng góp
   bool active;
   bool sendNotification;


  PromoDetail({
     this.id,
     this.bankCode,
     this.bankName,
     this.iconBank,
     this.scheme,
     this.bins,
     this.title,
     this.category,
     this.description,
     this.budget,
     this.startDate,
     this.endDate,
     this.promoImageUrl,
     this.promoCode,
     this.linkPromo,
     this.promoOptionAmountReduce,
     this.promoOptionAmountRefund,
     this.merchantId,
     this.merchantName,
     this.storeLocation,
     this.termsAndConditions,
     required this.active,
     required this.sendNotification,
    this.userNameContribution,
  });

  factory PromoDetail.fromJson(Map<String, dynamic> json) {
    return PromoDetail(
      id: json['id'],
      bankCode: json['bankCode'] ?? '',
      bankName: json['bankName'] ?? '',
      iconBank: json['iconBank'] ?? '',
      scheme:  json['scheme'] != null
          ? List<String>.from(json['scheme'])
          : [],
      bins:  json['bins'] != null
          ? List<int>.from(json['bins'])
          : [],
      title: json['title'] ?? '',
      category:  json['category'] != null
          ? List<String>.from(json['category'])
          : [],
      description: json['description'] ?? '',
      budget: json['budget'] ?? '',
      startDate: json['startDate'] ?? 0,
      endDate: json['endDate'] ?? 0,
      promoImageUrl: json['promoImageUrl'] ?? '',
      promoCode: json['promoCode'] ?? '',
      linkPromo: json['linkPromo'] ?? '',
      promoOptionAmountReduce: (json['promoOptionAmountReduce'] != null)
          ? (json['promoOptionAmountReduce'] as List)
          .map((e) => PromoOptionAmountReduce.fromJson(e))
          .toList()
          : [],
      promoOptionAmountRefund: (json['promoOptionAmountRefund'] != null)
          ? (json['promoOptionAmountRefund'] as List)
          .map((e) => PromoOptionAmountRefund.fromJson(e))
          .toList()
          : [],
      merchantId: json['merchantId'] ?? '',
      merchantName: json['merchantName'] ?? '',
      storeLocation: json['storeLocation'] ?? '',
      termsAndConditions: json['termsAndConditions'] ?? '',
      active: json['active'] ?? true,
      sendNotification: json['sendNotification'] ?? false,
      userNameContribution: json['userNameContribution'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'bankCode': bankCode,
      'bankName': bankName,
      'iconBank': iconBank,
      'scheme': scheme,
      'bins': bins,
      'title': title,
      'category': category,
      'description': description,
      'budget': budget,
      'startDate': startDate,
      'endDate': endDate,
      'promoImageUrl': promoImageUrl,
      'promoCode': promoCode,
      'linkPromo': linkPromo,
      'promoOptionAmountReduce': promoOptionAmountReduce?.map((e) => e.toJson()).toList(),
      'promoOptionAmountRefund': promoOptionAmountRefund?.map((e) => e.toJson()).toList(),
      'merchantId': merchantId,
      'merchantName': merchantName,
      'storeLocation': storeLocation,
      'termsAndConditions': termsAndConditions,
      'active': active,
      'sendNotification': sendNotification,
      'userNameContribution': userNameContribution,
    };
  }

  String? get startDateAsDate {
    if (startDate == null || startDate == 0) return null;
    final dt = DateTime.fromMillisecondsSinceEpoch(startDate!);
    // return DateTime(dt.year, dt.month, dt.day);
    return DateFormat('yyyy-MM-dd').format(DateTime(dt.year, dt.month, dt.day));
  }

  String? get endDateAsDate {
    if (endDate == null || endDate == 0) return null;
    final dt = DateTime.fromMillisecondsSinceEpoch(endDate!);
    return DateFormat('yyyy-MM-dd').format(DateTime(dt.year, dt.month, dt.day));
  }
}

class PromoOptionAmountReduce {
   int minAmount;
   int fixAmountReduce;
   int rateAmountReduce;
   int maxAmountReduce;

  PromoOptionAmountReduce({
    required this.minAmount,
    required this.fixAmountReduce,
    required this.rateAmountReduce,
    required this.maxAmountReduce,
  });

  factory PromoOptionAmountReduce.fromJson(Map<String, dynamic> json) {
    return PromoOptionAmountReduce(
      minAmount: json['minAmount'] ?? 0,
      fixAmountReduce: json['fixAmountReduce'] ?? 0,
      rateAmountReduce: json['rateAmountReduce'] ?? 0,
      maxAmountReduce: json['maxAmountReduce'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'minAmount': minAmount,
      'fixAmountReduce': fixAmountReduce,
      'rateAmountReduce': rateAmountReduce,
      'maxAmountReduce': maxAmountReduce,
    };
  }
}

class PromoOptionAmountRefund {
   int minAmount;
   int fixAmountRefund;
   int rateAmountRefund;
   int maxAmountRefund;

  PromoOptionAmountRefund({
    required this.minAmount,
    required this.fixAmountRefund,
    required this.rateAmountRefund,
    required this.maxAmountRefund,
  });

  factory PromoOptionAmountRefund.fromJson(Map<String, dynamic> json) {
    return PromoOptionAmountRefund(
      minAmount: json['minAmount'] ?? 0,
      fixAmountRefund: json['fixAmountRefund'] ?? 0,
      rateAmountRefund: json['rateAmountRefund'] ?? 0,
      maxAmountRefund: json['maxAmountRefund'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'minAmount': minAmount,
      'fixAmountRefund': fixAmountRefund,
      'rateAmountRefund': rateAmountRefund,
      'maxAmountRefund': maxAmountRefund,
    };
  }
}
