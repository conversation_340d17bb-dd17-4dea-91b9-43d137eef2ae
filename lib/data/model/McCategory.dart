class McCategory {
  final String? key;
  final String? name_vi;
  final String? name_en;

  McCategory({
    this.key,
    this.name_vi,
    this.name_en,
  });

  factory McCategory.fromJson(Map<dynamic, dynamic> json) =>
      McCategory(
        key: json["key"] ?? '',
        name_vi: json["name_vi"] ?? '',
        name_en: json["name_en"] ?? ''
      );

  Map<dynamic, dynamic> toJson() =>
      {
        "key": key,
        "name_vi": name_vi,
        "name_en": name_en
      };
}