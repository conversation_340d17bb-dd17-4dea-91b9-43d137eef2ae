import 'package:card_promo/data/model/promo_response.dart';

import 'banner.dart';

class PromoAllConfig {
  final List<BannerData> banner;
  final PromoResponse promo;


  PromoAllConfig({
    required this.banner,
    required this.promo,
  });

  factory PromoAllConfig.fromJson(Map<String, dynamic> json) => PromoAllConfig(
    banner: List<BannerData>.from(
      (json["banner"] as List).map((x) => BannerData.fromJson(x)),
    ),
    promo: PromoResponse.fromJson(json["promo"]),
  );

  Map<String, dynamic> toJson() => {
    "banner": List<dynamic>.from(banner.map((x) => x.toJson())),
    "promo": promo.toJson(),
  };
}