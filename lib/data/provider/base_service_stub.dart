import '../model/responseData.dart';
import 'base_service_interface.dart';

class BaseService extends BaseServiceInterface {
  static final BaseService _instance = BaseService._internal();
  factory BaseService() => _instance;
  BaseService._internal();

  @override
  Future<String> sendRequest(String url, String data) async => _notSupported();
  @override
  Future<ResponseData> sendData(String urlCall, String? jsonObject) async => _notSupported();
  @override
  Future<ResponseData> getData(String urlCall) async => _notSupported();
  @override
  Future<Map<String, dynamic>> getDataWeb(String url) async => _notSupported();
  @override
  Future<Map<String, dynamic>> postDataWeb(String url, Map<String, dynamic> body) async => _notSupported();
  // @override
  // Future<Map<String, dynamic>> fetchDataWeb(String url, Map<String, dynamic> body) async => _notSupported();

  T _notSupported<T>() => throw UnimplementedError("BaseService chưa được hỗ trợ trên nền tảng này.");
}