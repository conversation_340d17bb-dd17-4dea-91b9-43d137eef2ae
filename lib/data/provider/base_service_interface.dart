import '../model/responseData.dart';

abstract class BaseServiceInterface {
  String authorizationHeader = "";

  Map<String, String> get headers => {
    'Content-Type': 'application/json',
    'Accept': '*',
    'User-Agent': 'PostmanRuntime/7.43.3',
  };

  Future<String> sendRequest(String url, String data);
  Future<ResponseData> sendData(String urlCall, String? jsonObject);
  Future<ResponseData> getData(String urlCall);

  Future<Map<String, dynamic>> getDataWeb(String url);
  Future<Map<String, dynamic>> postDataWeb(String url, Map<String, dynamic> body);
  // Future<Map<String, dynamic>> fetchDataWeb(String url, Map<String, dynamic> body);
}
