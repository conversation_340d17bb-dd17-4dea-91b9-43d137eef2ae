import 'dart:convert';
import 'dart:io';
import 'dart:developer' as develop;

import 'package:card_promo/data/provider/base_service_interface.dart';
import 'package:card_promo/util/api_constant.dart';

import '../../build_constants.dart';
import '../../util/RSAEncryption.dart';
import '../../util/crypto_helper.dart';
import '../../util/local_storage.dart';
import '../../util/ufo_logger.dart';
import '../model/errorResponse.dart';
import '../model/responseData.dart';

class BaseService extends BaseServiceInterface{
  static final BaseService _singleton = BaseService._internal();

  factory BaseService() {
    return _singleton;
  }

  BaseService._internal();

  String _domainPushLog = "";

  Future<void> pushLogDetail(String? detailLog) async {
    if (detailLog != null) {
      Map<String, dynamic> dataObject = Map();
      dataObject['serviceName'] = 'SAVE_LOG_ACTION';
      dataObject['description'] = detailLog;
      dataObject['os'] = Platform.operatingSystem;
      String jsonObject = jsonEncode(dataObject);
      var response = await sendData(_domainPushLog, jsonObject);
      Map<String, dynamic> jsonResponse = jsonDecode(response.responseData);
      ErrorResponse errorResponse = ErrorResponse.paserData(jsonResponse);
      if (errorResponse.code == ApiConstant.DO_SERVICE_SUCCESS) {
        UfoLogger().clearLog();
      }
      UfoLogger().writeLog("Response: $response");
    }
  }

  @override
  Future<ResponseData> sendData(String urlCall, String? jsonObject) async {
    try {
      HttpClient client = new HttpClient();
      client.connectionTimeout =
          Duration(seconds: 18);
      client.badCertificateCallback =
          ((X509Certificate cert, String host, int port) => true);
      Uri uri = Uri.parse(BuildConstants.serverAPI + urlCall);

      UfoLogger().writeLog("[==========URL HOST: ${uri.host}==========]");
      HttpClientRequest request = await client.postUrl(uri);
      if (BuildConstants.serverAPI + urlCall != _domainPushLog) {
        UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_REQUEST,
            '[=========PROMO PATH: ${uri.path} | Auth: ${authorizationHeader.isNotEmpty ? "E" : "N"} ==========] \nRequest data: $jsonObject');
      }
      if (authorizationHeader.isNotEmpty) {
        request.headers.set("Authorization", "Bearer $authorizationHeader");
      }
      request.headers.set('content-type', 'application/json');
      if (jsonObject != null) {
        String encryptedMessage = await RSAEncryption.encryptAES_ECB(jsonObject);
        print('encryptData: $encryptedMessage');
        request.add(utf8.encode(encryptedMessage));
      }
      HttpClientResponse response = await request.close();
      String strResponse = await response.transform(utf8.decoder).join();
      if (response.statusCode == 401) {
        // await SystemPreference.clearPreference(
        //     SystemPreference.STORAGE_TOKEN);
        authorizationHeader = "";
      }
      UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_RESPONSE, strResponse);

      if (response.statusCode == 200 && strResponse.isNotEmpty) {
        try {
          strResponse = await RSAEncryption.doAESDecrypt(strResponse);
          print('decryptData: $strResponse');
        } catch (e) {
          print('Error decrypting response: $urlCall - $e');
        }
      }

      return ResponseData(
          responseData: strResponse,
          errorResponse: ErrorResponse(code: response.statusCode));
    } catch (exception) {
      UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_RESPONSE,
          '[========== Exception: ==========]\n${exception.toString()}');
      return ResponseData(
          responseData: "", errorResponse: ErrorResponse(code: -1, message: 'Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối mạng của bạn.'));
    }
  }

  @override
  Future<ResponseData> getData(String urlCall) async {
    try {
      HttpClient client = HttpClient();
      client.connectionTimeout = Duration(seconds: 18);
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;

      Uri uri = Uri.parse(BuildConstants.serverAPI + urlCall);
      UfoLogger().writeLog("[==========URL HOST: ${uri.host}==========]");
      UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_REQUEST,
          '[==========PROMO PATH: ${uri.path} | Auth: ${authorizationHeader.isNotEmpty ? "E" : "N"} ==========]');

      HttpClientRequest request = await client.getUrl(uri);

      // Add headers
      if (authorizationHeader.isNotEmpty) {
        request.headers.set("Authorization", "Bearer $authorizationHeader");
      }
      request.headers.set('content-type', 'application/json');

      // Handle response
      HttpClientResponse response = await request.close();
      String? strResponse = await response.transform(utf8.decoder).join();

      // UfoLogger()().writeLog(ApiConstant.UfoLogger()_TYPE_RESPONSE,
      //     'Response: $strResponse\n[========== Code: ${response.statusCode}==========]');

      // Check response status and return accordingly
      if (response.statusCode == 200) {
        return ResponseData(responseData: strResponse, errorResponse: ErrorResponse(code: 200));
      } else {
        return ResponseData(
          responseData: "",
          errorResponse: ErrorResponse(code: response.statusCode, message: 'Error occurred'),
        );
      }

    } catch (e) {
      UfoLogger().writeLog(action: ApiConstant.LOGGER_TYPE_RESPONSE,
          '[========== Exception: ==========]\n${e.toString()}');
      return ResponseData(
        responseData: "",
        errorResponse: ErrorResponse(code: -1, message: e.toString()),
      );
    }
  }

  // @override
  // Future<Map<String, dynamic>> fetchDataWeb(String url, Map<String, dynamic> body) {
  //   // TODO: implement fetchDataWeb
  //   throw UnimplementedError();
  // }

  @override
  Future<Map<String, dynamic>> getDataWeb(String url) {
    // TODO: implement getDataWeb
    throw UnimplementedError();
  }

  @override
  Future<Map<String, dynamic>> postDataWeb(String url, Map<String, dynamic> body) {
    // TODO: implement postDataWeb
    throw UnimplementedError();
  }

  @override
  Future<String> sendRequest(String url, String data) {
    // TODO: implement sendRequest
    throw UnimplementedError();
  }
}
