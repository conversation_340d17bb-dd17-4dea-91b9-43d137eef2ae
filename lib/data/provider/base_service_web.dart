import 'dart:convert';
import 'dart:async';
import 'dart:html' as html;
import 'package:card_promo/data/model/responseData.dart';
import 'package:card_promo/data/provider/base_service_interface.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import '../../build_constants.dart';
import '../../util/RSAEncryption.dart';

class BaseService extends BaseServiceInterface {
  static final BaseService _instance = BaseService._internal();
  factory BaseService() => _instance;
  BaseService._internal();

  @override
  Future<Map<String, dynamic>> getDataWeb(String url) async {
    try {
      print('Calling GET: ${BuildConstants.serverAPI + url}');

      // Sử dụng XMLHttpRequest trực tiếp khi chạy trên web
      if (kIsWeb) {
        final completer = Completer<Map<String, dynamic>>();
        final xhr = html.HttpRequest();

        xhr.open('GET', BuildConstants.serverAPI + url);
        headers.forEach((key, value) {
          xhr.setRequestHeader(key, value);
        });

        xhr.onLoad.listen((event) {
          if (xhr.status == 200) {
            try {
              final responseText = xhr.responseText ?? '';
              final data = jsonDecode(responseText);
              completer.complete({
                'success': true,
                'data': data,
              });
            } catch (e) {
              completer.complete({
                'success': false,
                'error': 'Invalid JSON response: ${e.toString()}',
                'rawResponse': xhr.responseText ?? '',
              });
            }
          } else {
            completer.complete({
              'success': false,
              'error': 'Request failed with status: ${xhr.status}',
              'statusCode': xhr.status,
              'rawResponse': xhr.responseText ?? '',
            });
          }
        });

        xhr.onError.listen((event) {
          completer.complete({
            'success': false,
            'error': 'XMLHttpRequest error: ${xhr.statusText ?? 'Unknown error'}',
            'rawResponse': xhr.responseText ?? '',
          });
        });

        xhr.send();
        return completer.future;
      }

      // Sử dụng http package cho non-web platforms
      final response = await http.get(
        Uri.parse(BuildConstants.serverAPI + url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);
          return {
            'success': true,
            'data': data,
          };
        } catch (e) {
          return {
            'success': false,
            'error': 'Invalid JSON response: ${e.toString()}',
            'rawResponse': response.body,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Request failed with status: ${response.statusCode}',
          'statusCode': response.statusCode,
          'rawResponse': response.body,
        };
      }
    } catch (e) {
      print('Error in getData: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> postDataWeb(String url, Map<String, dynamic> body) async {
    try {
      print('Calling POST: ${BuildConstants.serverAPI + url}');
      final jsonBody = jsonEncode(body);
      print('param: ${jsonBody}');

      // String encryptedMessage = RSAEncryption.encryptData(jsonBody);
      // String encryptedMessage = await RSAEncryption.doAESEncrypt(jsonBody);
      String encryptedMessage = await RSAEncryption.encryptAES_ECB(jsonBody);
      print('encryptData: $encryptedMessage');

      if (kIsWeb) {
        final completer = Completer<Map <String, dynamic>>();
        final xhr = html.HttpRequest();
        xhr.open('POST', BuildConstants.serverAPI + url);
        headers.forEach((key, value) {
          xhr.setRequestHeader(key, value);
        });

        xhr.onLoad.listen((event) async {
          if (xhr.status == 200) {
            try {
              final responseText = xhr.responseText ?? '';
              var decryptData = '';
              var data = {};
              try {
                decryptData = await RSAEncryption.doAESDecrypt(responseText);
                print('decryptData: $decryptData');
                data = jsonDecode(decryptData);
              } catch (e) {
                print('Can not decrypting response: $e');
                data = jsonDecode(responseText);
              }
              completer.complete({
                'success': true,
                'data': data,
              });
            } catch (e) {
              completer.complete({
                'success': false,
                'error': 'Invalid JSON response: ${e.toString()}',
                'rawResponse': xhr.responseText ?? '',
              });
            }
          } else {
            completer.complete({
              'success': false,
              'error': 'Request failed with status: ${xhr.status}',
              'statusCode': xhr.status,
              'rawResponse': xhr.responseText ?? '',
            });
          }
        });

        xhr.onError.listen((event) {
          completer.complete({
            'success': false,
            'error': 'XMLHttpRequest error: ${xhr.statusText ?? 'Unknown error'}',
            'rawResponse': xhr.responseText ?? '',
          });
        });

        xhr.send(encryptedMessage);
        return completer.future;
      }

      final response = await http.post(
        Uri.parse(BuildConstants.serverAPI + url),
        headers: headers,
        body: encryptedMessage,
      );

      if (response.statusCode == 200) {
        try {
          var data = '';
          try {
            data = await RSAEncryption.doAESDecrypt(jsonDecode(response.body));
            print('decryptData: $data');
          } catch (e) {
            print('Error decrypting response: $e');
          }
          return {
            'success': true,
            'data': data,
          };
        } catch (e) {
          return {
            'success': false,
            'error': 'Invalid JSON response: ${e.toString()}',
            'rawResponse': response.body,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Request failed with status: ${response.statusCode}',
          'statusCode': response.statusCode,
          'rawResponse': response.body,
        };
      }
    } catch (e) {
      print('Error in postData: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> putData(String url, Map<String, dynamic> body) async {
    try {
      print('Calling PUT: ${BuildConstants.serverAPI + url}');
      final jsonBody = jsonEncode(body);

      if (kIsWeb) {
        final completer = Completer<Map<String, dynamic>>();
        final xhr = html.HttpRequest();

        xhr.open('PUT', BuildConstants.serverAPI + url);
        headers.forEach((key, value) {
          xhr.setRequestHeader(key, value);
        });

        xhr.onLoad.listen((event) {
          if (xhr.status == 200) {
            try {
              final responseText = xhr.responseText ?? '';
              final data = jsonDecode(responseText);
              completer.complete({
                'success': true,
                'data': data,
              });
            } catch (e) {
              completer.complete({
                'success': false,
                'error': 'Invalid JSON response: ${e.toString()}',
                'rawResponse': xhr.responseText ?? '',
              });
            }
          } else {
            completer.complete({
              'success': false,
              'error': 'Request failed with status: ${xhr.status}',
              'statusCode': xhr.status,
              'rawResponse': xhr.responseText ?? '',
            });
          }
        });

        xhr.onError.listen((event) {
          completer.complete({
            'success': false,
            'error': 'XMLHttpRequest error: ${xhr.statusText ?? 'Unknown error'}',
            'rawResponse': xhr.responseText ?? '',
          });
        });

        xhr.send(jsonBody);
        return completer.future;
      }

      final response = await http.put(
        Uri.parse(BuildConstants.serverAPI + url),
        headers: headers,
        body: jsonBody,
      );

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);
          return {
            'success': true,
            'data': data,
          };
        } catch (e) {
          return {
            'success': false,
            'error': 'Invalid JSON response: ${e.toString()}',
            'rawResponse': response.body,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Request failed with status: ${response.statusCode}',
          'statusCode': response.statusCode,
          'rawResponse': response.body,
        };
      }
    } catch (e) {
      print('Error in putData: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  @override
  Future<Map<String, dynamic>> deleteData(String url) async {
    try {
      print('Calling DELETE: ${BuildConstants.serverAPI + url}');

      if (kIsWeb) {
        final completer = Completer<Map<String, dynamic>>();
        final xhr = html.HttpRequest();

        xhr.open('DELETE', BuildConstants.serverAPI + url);
        headers.forEach((key, value) {
          xhr.setRequestHeader(key, value);
        });

        xhr.onLoad.listen((event) {
          if (xhr.status == 200) {
            try {
              final responseText = xhr.responseText ?? '';
              final data = jsonDecode(responseText);
              completer.complete({
                'success': true,
                'data': data,
              });
            } catch (e) {
              completer.complete({
                'success': false,
                'error': 'Invalid JSON response: ${e.toString()}',
                'rawResponse': xhr.responseText ?? '',
              });
            }
          } else {
            completer.complete({
              'success': false,
              'error': 'Request failed with status: ${xhr.status}',
              'statusCode': xhr.status,
              'rawResponse': xhr.responseText ?? '',
            });
          }
        });

        xhr.onError.listen((event) {
          completer.complete({
            'success': false,
            'error': 'XMLHttpRequest error: ${xhr.statusText ?? 'Unknown error'}',
            'rawResponse': xhr.responseText ?? '',
          });
        });

        xhr.send();
        return completer.future;
      }

      final response = await http.delete(
        Uri.parse(BuildConstants.serverAPI + url),
        headers: headers,
      );

      if (response.statusCode == 200) {
        try {
          final data = jsonDecode(response.body);
          return {
            'success': true,
            'data': data,
          };
        } catch (e) {
          return {
            'success': false,
            'error': 'Invalid JSON response: ${e.toString()}',
            'rawResponse': response.body,
          };
        }
      } else {
        return {
          'success': false,
          'error': 'Request failed with status: ${response.statusCode}',
          'statusCode': response.statusCode,
          'rawResponse': response.body,
        };
      }
    } catch (e) {
      print('Error in deleteData: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  // @override
  // Future<Map<String, dynamic>> fetchDataWeb(String url) async {
  //   try {
  //     if (kIsWeb) {
  //       // Sử dụng XMLHttpRequest cho Flutter Web
  //       final completer = Completer<Map<String, dynamic>>();
  //       final xhr = html.HttpRequest();
  //       xhr.open('GET', BuildConstants.serverAPI + url);
  //
  //       print('https: ${BuildConstants.serverAPI + url}');
  //       // Nếu cần set header, ví dụ:
  //       xhr.setRequestHeader('Content-Type', 'application/json');
  //
  //       xhr.onLoad.listen((event) {
  //         if (xhr.status == 200) {
  //           try {
  //             final data = jsonDecode(xhr.responseText ?? '');
  //             completer.complete({
  //               'success': true,
  //               'data': data,
  //             });
  //           } catch (e) {
  //             completer.complete({
  //               'success': false,
  //               'error': 'Invalid JSON response: ${e.toString()}',
  //               'rawResponse': xhr.responseText ?? '',
  //             });
  //           }
  //         } else {
  //           completer.complete({
  //             'success': false,
  //             'error': 'Request failed with status: ${xhr.status}',
  //             'statusCode': xhr.status,
  //             'rawResponse': xhr.responseText ?? '',
  //           });
  //         }
  //       });
  //
  //       xhr.onError.listen((event) {
  //         completer.complete({
  //           'success': false,
  //           'error': 'XMLHttpRequest error: ${xhr.statusText ?? 'Unknown error'}',
  //           'rawResponse': xhr.responseText ?? '',
  //         });
  //       });
  //
  //       xhr.send();
  //       return completer.future;
  //     } else {
  //       // Sử dụng package http cho non-web platform
  //       final response = await http.get(Uri.parse(url));
  //       if (response.statusCode == 200) {
  //         try {
  //           final data = jsonDecode(response.body);
  //           return {
  //             'success': true,
  //             'data': data,
  //           };
  //         } catch (e) {
  //           return {
  //             'success': false,
  //             'error': 'Invalid JSON response: ${e.toString()}',
  //             'rawResponse': response.body,
  //           };
  //         }
  //       } else {
  //         return {
  //           'success': false,
  //           'error': 'Request failed with status: ${response.statusCode}',
  //           'statusCode': response.statusCode,
  //           'rawResponse': response.body,
  //         };
  //       }
  //     }
  //   } catch (e) {
  //     return {
  //       'success': false,
  //       'error': e.toString(),
  //     };
  //   }
  // }

  @override
  Future<ResponseData> getData(String urlCall) {
    // TODO: implement getData
    throw UnimplementedError();
  }

  @override
  Future<ResponseData> sendData(String urlCall, String? jsonObject) {
    // TODO: implement sendData
    throw UnimplementedError();
  }

  @override
  Future<String> sendRequest(String url, String data) {
    // TODO: implement sendRequest
    throw UnimplementedError();
  }
}