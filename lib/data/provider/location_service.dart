import 'package:location/location.dart';

class LocationService {
  final Location _location = Location();

  Future<LocationData?> getCurrentLocation() async {
    bool serviceEnabled;
    PermissionStatus permissionGranted;

    // Kiểm tra dịch vụ vị trí có bật không
    serviceEnabled = await _location.serviceEnabled();
    if (!serviceEnabled) {
      serviceEnabled = await _location.requestService();
      if (!serviceEnabled) {
        return null; // Nếu dịch vụ bị tắt, trả về null
      }
    }

    // Kiểm tra quyền truy cập vị trí
    permissionGranted = await _location.hasPermission();
    if (permissionGranted == PermissionStatus.denied) {
      permissionGranted = await _location.requestPermission();
      if (permissionGranted != PermissionStatus.granted) {
        return null; // Nếu không được cấp quyền, tr<PERSON> về null
      }
    }

    // L<PERSON>y vị trí hiện tại
    return await _location.getLocation();
  }
}
