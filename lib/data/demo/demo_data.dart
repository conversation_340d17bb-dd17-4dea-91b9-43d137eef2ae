import '../model/merchant.dart';
import '../model/promo_detail.dart';

class DemoData {
  // Demo merchants data
  static List<Merchant> getDemoMerchants() {
    return [
      <PERSON>(
        id: '1',
        name: 'Circle K',
        email: '<EMAIL>',
        phone: '1900 6936',
        locations: [
          MerchantLocation(
            branchName: 'Circle K Nguyễn Huệ',
            address: '123 Nguyễn Huệ, Q1, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7769,
              y: 106.7009,
              type: 'Point',
              coordinates: [106.7009, 10.7769],
            ),
          ),
        ],
      ),
      <PERSON>(
        id: '2',
        name: 'Starbucks Coffee',
        email: '<EMAIL>',
        phone: '1800 6779',
        locations: [
          MerchantLocation(
            branchName: 'Starbucks Landmark 81',
            address: 'Landmark 81, Q. Bình Thạnh, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7946,
              y: 106.7218,
              type: 'Point',
              coordinates: [106.7218, 10.7946],
            ),
          ),
        ],
      ),
      <PERSON>(
        id: '3',
        name: 'KFC Vietnam',
        email: '<EMAIL>',
        phone: '1900 5555',
        locations: [
          MerchantLocation(
            branchName: 'KFC Vincom Center',
            address: '72 Lê Thánh Tôn, Q1, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7829,
              y: 106.7025,
              type: 'Point',
              coordinates: [106.7025, 10.7829],
            ),
          ),
        ],
      ),
      Merchant(
        id: '4',
        name: 'McDonald\'s',
        email: '<EMAIL>',
        phone: '1900 6717',
        locations: [
          MerchantLocation(
            branchName: 'McDonald\'s Saigon Centre',
            address: '65 Lê Lợi, Q1, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7740,
              y: 106.7010,
              type: 'Point',
              coordinates: [106.7010, 10.7740],
            ),
          ),
        ],
      ),
      Merchant(
        id: '5',
        name: 'Lotte Mart',
        email: '<EMAIL>',
        phone: '1900 5888',
        locations: [
          MerchantLocation(
            branchName: 'Lotte Mart Cộng Hòa',
            address: '469 Nguyễn Thị Minh Khai, Q3, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7860,
              y: 106.6917,
              type: 'Point',
              coordinates: [106.6917, 10.7860],
            ),
          ),
        ],
      ),
      Merchant(
        id: '6',
        name: 'CGV Cinemas',
        email: '<EMAIL>',
        phone: '1900 6017',
        locations: [
          MerchantLocation(
            branchName: 'CGV Vincom Center',
            address: '72 Lê Thánh Tôn, Q1, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7829,
              y: 106.7025,
              type: 'Point',
              coordinates: [106.7025, 10.7829],
            ),
          ),
        ],
      ),
      Merchant(
        id: '7',
        name: 'Highlands Coffee',
        email: '<EMAIL>',
        phone: '1800 6779',
        locations: [
          MerchantLocation(
            branchName: 'Highlands Coffee Bitexco',
            address: '2 Hải Triều, Q1, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7718,
              y: 106.7045,
              type: 'Point',
              coordinates: [106.7045, 10.7718],
            ),
          ),
        ],
      ),
      Merchant(
        id: '8',
        name: 'Pizza Hut',
        email: '<EMAIL>',
        phone: '1900 6099',
        locations: [
          MerchantLocation(
            branchName: 'Pizza Hut Nguyễn Trãi',
            address: '123 Nguyễn Trãi, Q1, TP.HCM',
            location: GeoJsonPoint(
              x: 10.7640,
              y: 106.6958,
              type: 'Point',
              coordinates: [106.6958, 10.7640],
            ),
          ),
        ],
      ),
    ];
  }

  // Demo promos data
  static List<PromoDetail> getDemoPromos() {
    final now = DateTime.now();
    final startDate = now.millisecondsSinceEpoch;
    final endDate = now.add(Duration(days: 30)).millisecondsSinceEpoch;

    return [
      PromoDetail(
        id: 'promo_1',
        bankCode: 'VCB',
        bankName: 'Vietcombank',
        iconBank: 'https://example.com/vcb_icon.png',
        scheme: ['VISA', 'MASTERCARD'],
        bins: [970436, 970448],
        title: 'Giảm 20% tại Circle K',
        category: ['F&B', 'Convenience Store'],
        description: 'Giảm 20% cho tất cả sản phẩm tại Circle K khi thanh toán bằng thẻ Vietcombank',
        budget: ********,
        startDate: startDate,
        endDate: endDate,
        promoImageUrl: 'https://example.com/promo1.jpg',
        promoCode: 'CIRCLEK20',
        linkPromo: 'https://circlek.vn/promotion',
        promoOptionAmountReduce: [
          PromoOptionAmountReduce(
            minAmount: 50000,
            fixAmountReduce: 0,
            rateAmountReduce: 20,
            maxAmountReduce: 100000,
          ),
        ],
        promoOptionAmountRefund: [],
        merchantId: '1',
        merchantName: 'Circle K',
        storeLocation: 'Tất cả cửa hàng Circle K',
        termsAndConditions: 'Áp dụng cho thẻ Vietcombank. Không áp dụng cùng khuyến mãi khác.',
        active: true,
        sendNotification: true,
        userNameContribution: 'Admin',
      ),
      PromoDetail(
        id: 'promo_2',
        bankCode: 'TCB',
        bankName: 'Techcombank',
        iconBank: 'https://example.com/tcb_icon.png',
        scheme: ['VISA'],
        bins: [970407],
        title: 'Mua 1 tặng 1 tại Starbucks',
        category: ['F&B', 'Coffee'],
        description: 'Mua 1 ly cà phê tặng 1 ly cùng loại tại Starbucks khi thanh toán bằng thẻ Techcombank',
        budget: 5000000,
        startDate: startDate,
        endDate: endDate,
        promoImageUrl: 'https://example.com/promo2.jpg',
        promoCode: 'STARBUCKS11',
        linkPromo: 'https://starbucks.vn/promotion',
        promoOptionAmountReduce: [],
        promoOptionAmountRefund: [],
        merchantId: '2',
        merchantName: 'Starbucks Coffee',
        storeLocation: 'Các cửa hàng Starbucks tại TP.HCM',
        termsAndConditions: 'Áp dụng cho thẻ Techcombank VISA. Chỉ áp dụng từ 9h-11h hàng ngày.',
        active: true,
        sendNotification: true,
        userNameContribution: 'Admin',
      ),
      PromoDetail(
        id: 'promo_3',
        bankCode: 'VCB',
        bankName: 'Vietcombank',
        iconBank: 'https://example.com/vcb_icon.png',
        scheme: ['MASTERCARD'],
        bins: [970436],
        title: 'Giảm 15% combo KFC',
        category: ['F&B', 'Fast Food'],
        description: 'Giảm 15% cho tất cả combo tại KFC khi thanh toán bằng thẻ Vietcombank Mastercard',
        budget: 8000000,
        startDate: startDate,
        endDate: endDate,
        promoImageUrl: 'https://example.com/promo3.jpg',
        promoCode: 'KFC15',
        linkPromo: 'https://kfc.com.vn/promotion',
        promoOptionAmountReduce: [
          PromoOptionAmountReduce(
            minAmount: 100000,
            fixAmountReduce: 0,
            rateAmountReduce: 15,
            maxAmountReduce: 50000,
          ),
        ],
        promoOptionAmountRefund: [],
        merchantId: '3',
        merchantName: 'KFC Vietnam',
        storeLocation: 'Tất cả cửa hàng KFC',
        termsAndConditions: 'Áp dụng cho thẻ Vietcombank Mastercard. Chỉ áp dụng cho combo.',
        active: true,
        sendNotification: false,
        userNameContribution: 'Admin',
      ),
      PromoDetail(
        id: 'promo_4',
        bankCode: 'BIDV',
        bankName: 'BIDV',
        iconBank: 'https://example.com/bidv_icon.png',
        scheme: ['VISA', 'MASTERCARD'],
        bins: [970418],
        title: 'Cashback 10% McDonald\'s',
        category: ['F&B', 'Fast Food'],
        description: 'Hoàn tiền 10% khi thanh toán tại McDonald\'s bằng thẻ BIDV',
        budget: ********,
        startDate: startDate,
        endDate: endDate,
        promoImageUrl: 'https://example.com/promo4.jpg',
        promoCode: 'MCDONALDS10',
        linkPromo: 'https://mcdonalds.vn/promotion',
        promoOptionAmountReduce: [],
        promoOptionAmountRefund: [
          PromoOptionAmountRefund(
            minAmount: 100000,
            fixAmountRefund: 0,
            rateAmountRefund: 10,
            maxAmountRefund: 50000,
          ),
        ],
        merchantId: '4',
        merchantName: 'McDonald\'s',
        storeLocation: 'Tất cả cửa hàng McDonald\'s',
        termsAndConditions: 'Áp dụng cho thẻ BIDV. Hoàn tiền trong vòng 7 ngày.',
        active: true,
        sendNotification: true,
        userNameContribution: 'Admin',
      ),
      PromoDetail(
        id: 'promo_5',
        bankCode: 'ACB',
        bankName: 'ACB',
        iconBank: 'https://example.com/acb_icon.png',
        scheme: ['MASTERCARD'],
        bins: [970416],
        title: 'Giảm 25% vé xem phim CGV',
        category: ['Entertainment', 'Cinema'],
        description: 'Giảm 25% giá vé xem phim tại CGV khi thanh toán bằng thẻ ACB Mastercard',
        budget: ********,
        startDate: startDate,
        endDate: endDate,
        promoImageUrl: 'https://example.com/promo5.jpg',
        promoCode: 'CGV25',
        linkPromo: 'https://cgv.vn/promotion',
        promoOptionAmountReduce: [
          PromoOptionAmountReduce(
            minAmount: 0,
            fixAmountReduce: 0,
            rateAmountReduce: 25,
            maxAmountReduce: 100000,
          ),
        ],
        promoOptionAmountRefund: [],
        merchantId: '6',
        merchantName: 'CGV Cinemas',
        storeLocation: 'Tất cả rạp CGV',
        termsAndConditions: 'Áp dụng cho thẻ ACB Mastercard. Không áp dụng vào cuối tuần.',
        active: true,
        sendNotification: true,
        userNameContribution: 'Admin',
      ),
    ];
  }

  // Get promos by merchant ID
  static List<PromoDetail> getPromosByMerchantId(String merchantId) {
    final allPromos = getDemoPromos();
    return allPromos.where((promo) => promo.merchantId == merchantId).toList();
  }

  // Search merchants by name
  static List<Merchant> searchMerchants(String query) {
    final allMerchants = getDemoMerchants();
    if (query.isEmpty) return allMerchants;
    
    return allMerchants.where((merchant) =>
      merchant.name.toLowerCase().contains(query.toLowerCase())
    ).toList();
  }
}
