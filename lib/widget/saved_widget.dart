import 'package:flutter/material.dart';
import '../res/color/app_colors.dart';
import '../util/styles.dart';

class SavedWidget extends StatefulWidget {
  final String? message;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? iconColor;
  final SavedStyle style;
  final Duration? duration;
  final VoidCallback? onTap;
  final bool showAnimation;

  const SavedWidget({
    Key? key,
    this.message,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
    this.style = SavedStyle.card,
    this.duration,
    this.onTap,
    this.showAnimation = true,
  }) : super(key: key);

  @override
  _SavedWidgetState createState() => _SavedWidgetState();
}

class _SavedWidgetState extends State<SavedWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.showAnimation) {
      _animationController.forward();
    }

    // Auto hide after duration
    if (widget.duration != null) {
      Future.delayed(widget.duration!, () {
        if (mounted) {
          _animationController.reverse();
        }
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.showAnimation) {
      return _buildContent();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: _buildContent(),
          ),
        );
      },
    );
  }

  Widget _buildContent() {
    switch (widget.style) {
      case SavedStyle.card:
        return _buildCardStyle();
      case SavedStyle.chip:
        return _buildChipStyle();
      case SavedStyle.banner:
        return _buildBannerStyle();
      case SavedStyle.floating:
        return _buildFloatingStyle();
      case SavedStyle.minimal:
        return _buildMinimalStyle();
    }
  }

  Widget _buildCardStyle() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(16),
        color: widget.backgroundColor ?? AppColors.lightGreen,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                colors: [
                  (widget.backgroundColor ?? AppColors.lightGreen).withOpacity(0.9),
                  (widget.backgroundColor ?? AppColors.lightGreen),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppColors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    widget.icon ?? Icons.check_circle_rounded,
                    color: widget.iconColor ?? AppColors.white,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Flexible(
                  child: Text(
                    widget.message ?? 'Đã lưu',
                    style: style_S14_W600_WhiteColor.copyWith(
                      color: widget.textColor ?? AppColors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChipStyle() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Material(
        elevation: 2,
        borderRadius: BorderRadius.circular(20),
        color: widget.backgroundColor ?? AppColors.lightGreen,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  widget.icon ?? Icons.check_circle_rounded,
                  color: widget.iconColor ?? AppColors.white,
                  size: 16,
                ),
                SizedBox(width: 6),
                Text(
                  widget.message ?? 'Đã lưu',
                  style: style_S12_W600_WhiteColor.copyWith(
                    color: widget.textColor ?? AppColors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBannerStyle() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? AppColors.lightGreen,
        boxShadow: [
          BoxShadow(
            color: AppColors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            widget.icon ?? Icons.check_circle_rounded,
            color: widget.iconColor ?? AppColors.white,
            size: 24,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              widget.message ?? 'Đã lưu thành công',
              style: style_S16_W600_WhiteColor.copyWith(
                color: widget.textColor ?? AppColors.white,
              ),
            ),
          ),
          if (widget.onTap != null)
            IconButton(
              onPressed: widget.onTap,
              icon: Icon(
                Icons.close_rounded,
                color: widget.iconColor ?? AppColors.white,
                size: 20,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildFloatingStyle() {
    return Positioned(
      bottom: 100,
      left: 20,
      right: 20,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(28),
        color: widget.backgroundColor ?? AppColors.lightGreen,
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.icon ?? Icons.check_circle_rounded,
                  color: widget.iconColor ?? AppColors.white,
                  size: 24,
                ),
                SizedBox(width: 12),
                Text(
                  widget.message ?? 'Đã lưu',
                  style: style_S16_W600_WhiteColor.copyWith(
                    color: widget.textColor ?? AppColors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMinimalStyle() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.icon ?? Icons.check_circle_rounded,
            color: widget.iconColor ?? AppColors.lightGreen,
            size: 16,
          ),
          SizedBox(width: 4),
          Text(
            widget.message ?? 'Đã lưu',
            style: style_S12_W400_BlackColor.copyWith(
              color: widget.textColor ?? AppColors.lightGreen,
            ),
          ),
        ],
      ),
    );
  }
}

enum SavedStyle {
  card,     // Card style with elevation and gradient
  chip,     // Small chip style
  banner,   // Full width banner
  floating, // Floating at bottom
  minimal,  // Just icon and text
}

// Predefined saved widgets for common use cases
class SavedWidgets {
  static Widget success({String? message, VoidCallback? onTap}) {
    return SavedWidget(
      message: message ?? 'Đã lưu thành công',
      icon: Icons.check_circle_rounded,
      backgroundColor: AppColors.lightGreen,
      style: SavedStyle.card,
      onTap: onTap,
      duration: Duration(seconds: 3),
    );
  }

  static Widget favourite({String? message, VoidCallback? onTap}) {
    return SavedWidget(
      message: message ?? 'Đã thêm vào yêu thích',
      icon: Icons.favorite_rounded,
      backgroundColor: AppColors.redText,
      style: SavedStyle.card,
      onTap: onTap,
      duration: Duration(seconds: 3),
    );
  }

  static Widget bookmark({String? message, VoidCallback? onTap}) {
    return SavedWidget(
      message: message ?? 'Đã đánh dấu',
      icon: Icons.bookmark_rounded,
      backgroundColor: AppColors.main_background,
      style: SavedStyle.card,
      onTap: onTap,
      duration: Duration(seconds: 3),
    );
  }

  static Widget chip({String? message, VoidCallback? onTap}) {
    return SavedWidget(
      message: message ?? 'Đã lưu',
      icon: Icons.check_rounded,
      backgroundColor: AppColors.lightGreen,
      style: SavedStyle.chip,
      onTap: onTap,
      showAnimation: false,
    );
  }

  static Widget floating({String? message, VoidCallback? onTap}) {
    return SavedWidget(
      message: message ?? 'Đã lưu thành công',
      icon: Icons.check_circle_rounded,
      backgroundColor: AppColors.lightGreen,
      style: SavedStyle.floating,
      onTap: onTap,
      duration: Duration(seconds: 4),
    );
  }

  static Widget minimal({String? message}) {
    return SavedWidget(
      message: message ?? 'Đã lưu',
      icon: Icons.check_rounded,
      style: SavedStyle.minimal,
      showAnimation: false,
    );
  }
}

// Status indicator widget for items
class SavedStatusWidget extends StatelessWidget {
  final bool isSaved;
  final String savedText;
  final String notSavedText;
  final IconData savedIcon;
  final IconData notSavedIcon;
  final Color savedColor;
  final Color notSavedColor;
  final VoidCallback? onTap;
  final bool showText;

  const SavedStatusWidget({
    Key? key,
    required this.isSaved,
    this.savedText = 'Đã lưu',
    this.notSavedText = 'Lưu',
    this.savedIcon = Icons.bookmark_rounded,
    this.notSavedIcon = Icons.bookmark_border_rounded,
    this.savedColor = const Color(0xFF4CAF50),
    this.notSavedColor = const Color(0xFF9E9E9E),
    this.onTap,
    this.showText = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: showText ? 12 : 8,
            vertical: 6,
          ),
          decoration: BoxDecoration(
            color: (isSaved ? savedColor : notSavedColor).withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: (isSaved ? savedColor : notSavedColor).withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isSaved ? savedIcon : notSavedIcon,
                color: isSaved ? savedColor : notSavedColor,
                size: 16,
              ),
              if (showText) ...[
                SizedBox(width: 6),
                Text(
                  isSaved ? savedText : notSavedText,
                  style: style_S12_W600_BlackColor.copyWith(
                    color: isSaved ? savedColor : notSavedColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

// Badge widget for saved items
class SavedBadgeWidget extends StatelessWidget {
  final String text;
  final IconData icon;
  final Color backgroundColor;
  final Color textColor;
  final double size;

  const SavedBadgeWidget({
    Key? key,
    this.text = 'Đã lưu',
    this.icon = Icons.check_circle_rounded,
    this.backgroundColor = const Color(0xFF4CAF50),
    this.textColor = Colors.white,
    this.size = 12,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withOpacity(0.3),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: textColor,
            size: size,
          ),
          SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: textColor,
              fontSize: size,
              fontWeight: FontWeight.w600,
              fontFamily: kFontFamilyBeVietnamPro,
            ),
          ),
        ],
      ),
    );
  }
}

// Animated save button
class AnimatedSaveButton extends StatefulWidget {
  final bool isSaved;
  final VoidCallback? onPressed;
  final String savedText;
  final String notSavedText;
  final IconData savedIcon;
  final IconData notSavedIcon;

  const AnimatedSaveButton({
    Key? key,
    required this.isSaved,
    this.onPressed,
    this.savedText = 'Đã lưu',
    this.notSavedText = 'Lưu',
    this.savedIcon = Icons.bookmark_rounded,
    this.notSavedIcon = Icons.bookmark_border_rounded,
  }) : super(key: key);

  @override
  _AnimatedSaveButtonState createState() => _AnimatedSaveButtonState();
}

class _AnimatedSaveButtonState extends State<AnimatedSaveButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.9).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Material(
            elevation: widget.isSaved ? 2 : 1,
            borderRadius: BorderRadius.circular(24),
            color: widget.isSaved ? AppColors.lightGreen : AppColors.grayBackground,
            child: InkWell(
              onTap: () {
                _controller.forward().then((_) {
                  _controller.reverse();
                });
                widget.onPressed?.call();
              },
              borderRadius: BorderRadius.circular(24),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      widget.isSaved ? widget.savedIcon : widget.notSavedIcon,
                      color: widget.isSaved ? AppColors.white : AppColors.greyTextContent,
                      size: 18,
                    ),
                    SizedBox(width: 8),
                    Text(
                      widget.isSaved ? widget.savedText : widget.notSavedText,
                      style: style_S14_W600_BlackColor.copyWith(
                        color: widget.isSaved ? AppColors.white : AppColors.greyTextContent,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
