import 'package:flutter/material.dart';
import '../res/color/app_colors.dart';
import '../util/styles.dart';
import 'saved_widget.dart';

class SavedWidgetDemo extends StatefulWidget {
  @override
  _SavedWidgetDemoState createState() => _SavedWidgetDemoState();
}

class _SavedWidgetDemoState extends State<SavedWidgetDemo> {
  bool isPromo1Saved = false;
  bool isPromo2Saved = true;
  bool isPromo3Saved = false;
  bool showSuccessMessage = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.grayBackground,
      appBar: AppBar(
        backgroundColor: AppColors.main_background,
        title: Text(
          'Saved Widget Demo',
          style: style_S20_W600_WhiteColor,
        ),
        centerTitle: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Success Messages Section
                Text(
                  'Success Messages',
                  style: style_S18_W600_BlackColor,
                ),
                SizedBox(height: 16),
                
                if (showSuccessMessage) SavedWidgets.success(),
                
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      showSuccessMessage = !showSuccessMessage;
                    });
                  },
                  child: Text('Toggle Success Message'),
                ),
                
                SizedBox(height: 24),
                
                // Different Styles Section
                Text(
                  'Different Styles',
                  style: style_S18_W600_BlackColor,
                ),
                SizedBox(height: 16),
                
                SavedWidgets.favourite(),
                SizedBox(height: 8),
                SavedWidgets.bookmark(),
                SizedBox(height: 8),
                Row(
                  children: [
                    SavedWidgets.chip(),
                    SizedBox(width: 8),
                    SavedWidgets.chip(message: 'Yêu thích'),
                    SizedBox(width: 8),
                    SavedWidgets.minimal(),
                  ],
                ),
                
                SizedBox(height: 24),
                
                // Status Widgets Section
                Text(
                  'Status Widgets',
                  style: style_S18_W600_BlackColor,
                ),
                SizedBox(height: 16),
                
                _buildPromoCard(
                  'Giảm 20% tại Circle K',
                  'Áp dụng cho thẻ Vietcombank',
                  isPromo1Saved,
                  () => setState(() => isPromo1Saved = !isPromo1Saved),
                ),
                
                _buildPromoCard(
                  'Mua 1 tặng 1 Starbucks',
                  'Áp dụng cho thẻ Techcombank',
                  isPromo2Saved,
                  () => setState(() => isPromo2Saved = !isPromo2Saved),
                ),
                
                _buildPromoCard(
                  'Cashback 10% McDonald\'s',
                  'Áp dụng cho thẻ BIDV',
                  isPromo3Saved,
                  () => setState(() => isPromo3Saved = !isPromo3Saved),
                ),
                
                SizedBox(height: 24),
                
                // Badge Examples
                Text(
                  'Badge Examples',
                  style: style_S18_W600_BlackColor,
                ),
                SizedBox(height: 16),
                
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: [
                    SavedBadgeWidget(),
                    SavedBadgeWidget(
                      text: 'Yêu thích',
                      icon: Icons.favorite_rounded,
                      backgroundColor: AppColors.redText,
                    ),
                    SavedBadgeWidget(
                      text: 'Đã đánh dấu',
                      icon: Icons.bookmark_rounded,
                      backgroundColor: AppColors.main_background,
                    ),
                    SavedBadgeWidget(
                      text: 'Đã thêm',
                      icon: Icons.add_circle_rounded,
                      backgroundColor: AppColors.blueText,
                    ),
                  ],
                ),
                
                SizedBox(height: 100), // Space for floating widget
              ],
            ),
          ),
          
          // Floating Action Button
          Positioned(
            bottom: 20,
            right: 20,
            child: FloatingActionButton.extended(
              onPressed: () {
                // Show floating saved message
                showDialog(
                  context: context,
                  barrierColor: Colors.transparent,
                  builder: (context) => SavedWidgets.floating(
                    onTap: () => Navigator.of(context).pop(),
                  ),
                );
              },
              backgroundColor: AppColors.main_background,
              icon: Icon(Icons.save_rounded, color: AppColors.white),
              label: Text(
                'Save',
                style: style_S14_W600_WhiteColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromoCard(String title, String subtitle, bool isSaved, VoidCallback onSaveToggle) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppColors.lightGreyText.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.main_background.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.local_offer_rounded,
                  color: AppColors.main_background,
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: style_S16_W600_BlackColor,
                    ),
                    SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: style_S14_W400_GreyColor,
                    ),
                  ],
                ),
              ),
              SavedStatusWidget(
                isSaved: isSaved,
                onTap: onSaveToggle,
              ),
            ],
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: AnimatedSaveButton(
                  isSaved: isSaved,
                  onPressed: onSaveToggle,
                ),
              ),
              SizedBox(width: 12),
              if (isSaved)
                SavedBadgeWidget(
                  size: 10,
                ),
            ],
          ),
        ],
      ),
    );
  }
}

// Usage examples for different scenarios
class SavedWidgetExamples {
  // For showing success after saving a promo
  static void showPromoSaved(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: SavedWidgets.success(
          message: 'Khuyến mãi đã được lưu',
          onTap: () => Navigator.of(context).pop(),
        ),
      ),
    );
  }

  // For showing in a snackbar
  static void showSavedSnackbar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: SavedWidgets.minimal(message: message),
        backgroundColor: Colors.transparent,
        elevation: 0,
        duration: Duration(seconds: 2),
      ),
    );
  }

  // For overlay at bottom of screen
  static void showFloatingSaved(BuildContext context) {
    OverlayEntry? overlayEntry;
    overlayEntry = OverlayEntry(
      builder: (context) => SavedWidgets.floating(
        onTap: () => overlayEntry?.remove(),
      ),
    );
    
    Overlay.of(context).insert(overlayEntry);
    
    // Auto remove after 3 seconds
    Future.delayed(Duration(seconds: 3), () {
      overlayEntry?.remove();
    });
  }
}
