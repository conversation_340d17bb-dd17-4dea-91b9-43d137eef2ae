import 'dart:convert';

import 'package:card_promo/app_controller.dart';
import 'package:card_promo/data/model/bank_data.dart';
import 'package:card_promo/data/model/promo_detail.dart';
import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/util/app_route.dart';
import 'package:card_promo/util/app_utils.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:get/get.dart';

import '../data/model/merchant.dart';
import '../res/image/app_images.dart';
import '../res/string/app_strings.dart';
import '../util/app_validation.dart';
import 'bankAndScheme.dart';

class DetailLocationWidget extends StatefulWidget {
  final VoidCallback onDismiss;
  final MerchantLocation merchantLocation;
  final List<PromoDetail> promoDetail;

  const DetailLocationWidget(
      {super.key,
      required this.onDismiss,
      required this.merchantLocation,
      required this.promoDetail});

  @override
  _DetailLocationWidgetState createState() => _DetailLocationWidgetState();
}

class _DetailLocationWidgetState extends State<DetailLocationWidget>
    with SingleTickerProviderStateMixin {
  late ScrollController _controller;
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Khởi tạo animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Thiết lập hiệu ứng trượt từ dưới lên
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.05, 1.0), // Bắt đầu từ dưới màn hình
      end: Offset.zero, // Kết thúc ở vị trí gốc
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );

    // Kích hoạt animation
    _animationController.forward();

    // Thiết lập scroll controller
    _controller = ScrollController();
    _controller.addListener(_scrollListener);
  }

  void _scrollListener() {
    if (_controller.offset <= 0 &&
        _controller.position.userScrollDirection == ScrollDirection.reverse) {
      print("Scroll từ top xuống (dùng ScrollController)");
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: _buildContent(),
    );
  }

  Widget _buildContent() {
    return Container(
      // height: MediaQuery.of(context).size.height / 7 * 3,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 15,
            spreadRadius: 2,
          )
        ],
      ),
      child: Column(
        children: [
          SizedBox(height: 12),
          Container(
            width: 40,
            height: 5,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          _buildDragHandle(),
          Expanded(
            child: (widget.promoDetail.length == 1)
                ? _onePromoDetail(widget.promoDetail[0])
                : _morePromoDetail(widget.promoDetail),
          ),
        ],
      ),
    );
  }

  // Các thành phần phụ
  Widget _buildDragHandle() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20),
      margin: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${widget.merchantLocation.branchName}',
                style: style_S20_W600_BlackColor,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              Text(
                '${widget.merchantLocation.address}',
                style: style_S14_W400_GreyColor,
                overflow: TextOverflow.ellipsis,
                maxLines: 2,
              ),
            ],
          )),
          TouchableWidget(
            padding: EdgeInsets.zero,
            width: 35,
            height: 35,
            // padding: EdgeInsets.all(5),
            decoration: BoxDecoration(
              color: AppColors.lightGreyText,
              borderRadius: BorderRadius.all(Radius.circular(20)),
            ),
            onPressed: () {
              setState(() {
                widget.onDismiss();
              });
            },
            child: Center(
                child: Icon(
              Icons.close,
              color: AppColors.white,
              size: 20,
            )),
          ),
        ],
      ),
    );
  }

  Widget _onePromoDetail(PromoDetail promo) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    BankandschemeWidget(
                      bank: promo.bankCode ?? '',
                      scheme: promo.scheme ?? [],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    _widgetOptionPromo(promo),
                    SizedBox(
                      height: 10,
                    ),
                    Text(
                      '${promo.description?.replaceAll('/n', '\n')}',
                      style: style_S14_W400_BlackColor,
                      // overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              )),
          TouchableWidget(
              height: 50,
              width: MediaQuery.of(context).size.width,
              margin: EdgeInsets.symmetric(horizontal: 5),
              decoration: BoxDecoration(
                  color: AppColors.ufo_color_background_button,
                  borderRadius: BorderRadius.circular(10)),
              onPressed: () {
                // controller.changeModeSignUp(false);
                Get.toNamed(
                  AppNameRoute.promo_detai_screen,
                  arguments: jsonEncode(promo),
                );
              },
              child: Text(
                AppStrings.getString(AppStrings.tt_detail),
                style: style_S16_W600_WhiteColor,
              )),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Row(
      children: [
        Icon(Icons.location_on, color: Colors.blue),
        SizedBox(width: 10),
        Text("Vị trí của bạn", style: TextStyle(fontSize: 16)),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildButton(Icons.directions, "Chỉ đường", Colors.blue),
        _buildButton(Icons.share, "Chia sẻ", Colors.green),
        _buildButton(Icons.favorite, "Yêu thích", Colors.red),
      ],
    );
  }

  Widget _buildDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 15),
        Text("Chi tiết địa điểm",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            )),
        SizedBox(height: 10),
        Text("Đây là mô tả chi tiết về địa điểm..."),
      ],
    );
  }

  Widget _buildButton(IconData icon, String text, Color color) {
    return Column(
      children: [
        IconButton(
          icon: Icon(icon, size: 30),
          color: color,
          onPressed: () {},
        ),
        Text(text, style: TextStyle(color: color)),
      ],
    );
  }

  bool _handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      if (notification.scrollDelta! > 0 && notification.metrics.pixels <= 0) {
        print("Scroll từ top xuống đã kích hoạt!");
      }
    }
    return false;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _controller.dispose();
    super.dispose();
  }

  Widget _morePromoDetail(List<PromoDetail> promoDetail) {
    return Container(
      child: ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: promoDetail.length,
          itemBuilder: (context, index) {
            PromoDetail data = promoDetail[index];
            return TouchableWidget(
              padding: EdgeInsets.zero,
              onPressed: () {
                Get.toNamed(
                  AppNameRoute.promo_detai_screen,
                  arguments: jsonEncode(data),
                );
              },
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 5, horizontal: 5),
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: AppColors.lightGreyText,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                
                                Row(
                                  children: [
                                    isNullEmpty(data.iconBank)
                                        ? Icon(Icons.account_balance, color: AppColors.main_background,)
                                        : Image.network(
                                      data.iconBank!,
                                      fit: BoxFit.contain,
                                      width: 50,
                                      height: 50,
                                      errorBuilder: (context, error, stackTrace) => Icon(
                                        Icons.account_balance,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                    SizedBox(width: 10,),
                                    Expanded(
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            '${data.title}',
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: style_S16_W600_BlackColor,
                                          ),
                                          SizedBox(
                                            height: 10,
                                          ),
                                          BankandschemeWidget(
                                            bank: getShortBankByCode(data.bankCode),
                                            scheme: data.scheme ?? [],
                                          ),
                                        ],
                                      ),
                                    )

                                  ],
                                ),
                                
                                SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  maxLines: 2,
                                  '${data.description?.replaceAll('/n', '\n')}',
                                  // data.description,
                                  overflow: TextOverflow.ellipsis,
                                  style: style_S14_W400_BlackColor,
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.navigate_next,
                            color: AppColors.ufo_color_background_button,
                          ),
                        ]),
                  ],
                ),
              ),
            );
          }),
    );
  }

  Widget _widgetOptionPromo(PromoDetail promo) {
    bool isReduce = (promo.promoOptionAmountReduce?.isNotEmpty ?? false);
    bool isRefund = (promo.promoOptionAmountRefund?.isNotEmpty ?? false);

    if (isReduce || isRefund) {
      // Xử lý khi có ít nhất một giá trị không rỗng
      return Container(
        padding: EdgeInsets.all(10),
        margin: EdgeInsets.only(top: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 5,
              spreadRadius: 2,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Ưu đãi khuyến mãi",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 5),
            isReduce ?
            Column(
              children:
              promo.promoOptionAmountReduce!.map((promo) {
                String promoText;
                if (promo.fixAmountReduce > 0) {
                  promoText =
                  "Giảm ${AppUtils.formatCurrency(promo.fixAmountReduce ~/ 1000)}K cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountReduce > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountReduce ~/ 1000)}K' : ''}";
                } else {
                  promoText =
                  "Giảm ${promo.rateAmountReduce}% cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountReduce > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountReduce ~/ 1000)}K' : ''}";
                }
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                          Icons.local_offer, color: Colors.redAccent, size: 18),
                      SizedBox(width: 6),
                      Text(
                        promoText,
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ) : Column(
              children:
              promo.promoOptionAmountRefund!.map((promo) {
                String promoText;
                if (promo.fixAmountRefund > 0) {
                  promoText =
                  "Hoàn ${AppUtils.formatCurrency(promo.fixAmountRefund ~/ 1000)}K cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountRefund > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountRefund ~/ 1000)}K' : ''}";
                } else {
                  promoText =
                  "Hoàn ${promo.rateAmountRefund}% cho đơn từ ${AppUtils.formatCurrency(promo
                      .minAmount ~/ 1000)}K ${promo.maxAmountRefund > 0 ? 'Tối đa ${AppUtils.formatCurrency(promo
                      .maxAmountRefund ~/ 1000)}K' : ''}";
                }
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                          Icons.local_offer, color: Colors.redAccent, size: 18),
                      SizedBox(width: 6),
                      Text(
                        promoText,
                        style: TextStyle(fontSize: 14),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      );
    } else {
      return SizedBox.shrink();
    }

    /*
    if (promo.promoOptionAmountReduce == null ||
        promo.promoOptionAmountReduce!.isEmpty) {
      return SizedBox.shrink();
    }
    return Container(
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.only(top: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 5,
            spreadRadius: 2,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Ưu đãi khuyến mãi",
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 5),
          Column(
            children: promo.promoOptionAmountReduce!.map((promo) {
              String promoText;
              if (promo.fixAmountReduce > 0) {
                promoText =
                    "Giảm ${promo.fixAmountReduce ~/ 1000}K cho đơn từ ${promo.minAmount ~/ 1000}K";
              } else {
                promoText =
                    "Giảm ${promo.rateAmountReduce}% cho đơn từ ${promo.minAmount ~/ 1000}K";
              }
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(Icons.local_offer, color: Colors.redAccent, size: 18),
                    SizedBox(width: 6),
                    Text(
                      promoText,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
     */
  }

  String getShortBankByCode(String? bankCode) {
    BankData? bank = Get.find<MyAppController>()
        .listBank
        .value
        .firstWhereOrNull((bank) => bank.bankCode == bankCode);
    return bank?.shortName ?? '';
  }
}
