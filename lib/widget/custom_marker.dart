import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/res/image/app_images.dart';
import 'package:card_promo/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

Future<BitmapDescriptor> createMarkerImage(String text) async {
  // 1. Thi<PERSON>t lập kích thước canvas
  const double canvasWidth = 240;
  const double canvasHeight = 320;

  final ui.PictureRecorder recorder = ui.PictureRecorder();
  final Canvas canvas = Canvas(recorder);

  // 2. Vẽ text phía trên
  final textStyle = TextStyle(
    color: AppColors.blackText,
    fontSize: 40, // tăng lên 40
    fontWeight: FontWeight.w800,
    fontFamily: kFontFamilyBeVietnamPro
  );
  final textSpan = TextSpan(text: text, style: textStyle);
  final textPainter = TextPainter(
    text: textSpan,
    textDirection: TextDirection.ltr,
    textAlign: TextAlign.center,
    maxLines: 2,
    ellipsis: '…',
  );
  textPainter.layout(minWidth: 0, maxWidth: canvasWidth);
  // Vẽ text ở y = 0, căn giữa theo x
  textPainter.paint(
    canvas,
    Offset((canvasWidth - textPainter.width) / 2, 0),
  );


  // 3. Load và vẽ icon dưới text
  //    Bạn cần có file assets/marker.png (icon location mặc định)
  final ByteData data = await rootBundle.load(AppImages.ic_marker);
  final Uint8List bytes = data.buffer.asUint8List();
  // scale icon lên 100px
  const double iconSize = 140;
  final ui.Codec codec = await ui.instantiateImageCodec(
    bytes,
    targetWidth: iconSize.toInt(),
    targetHeight: iconSize.toInt(),
  );
  final ui.FrameInfo fi = await codec.getNextFrame();
  // Vẽ icon ở y = textHeight + 10px khoảng cách
  final double iconY = textPainter.height + 10;
  canvas.drawImage(
    fi.image,
    Offset((canvasWidth - iconSize) / 2, iconY),
    Paint(),
  );

  // 4. Kết thúc và chuyển thành BitmapDescriptor
  final ui.Image finalImage =
  await recorder.endRecording().toImage(canvasWidth.toInt(), canvasHeight.toInt());
  final ByteData? pngData =
  await finalImage.toByteData(format: ui.ImageByteFormat.png);
  final Uint8List pngBytes = pngData!.buffer.asUint8List();

  return BitmapDescriptor.fromBytes(pngBytes);
}
