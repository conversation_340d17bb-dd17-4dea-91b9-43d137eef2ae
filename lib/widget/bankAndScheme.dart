import 'package:card_promo/res/color/app_colors.dart';
import 'package:flutter/material.dart';

import '../util/styles.dart';

class BankandschemeWidget extends StatelessWidget {
  final String bank;
  final List<String> scheme;

  const BankandschemeWidget({
    required this.bank,
    required this.scheme,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        bank.isNotEmpty ?
        Text(
          '${bank} ',
          style: style_S16_W600_BlackColor,
        ) : SizedBox.shrink(),
        Expanded(
          child: Wrap(
            spacing: 5, // Khoảng cách giữa các Container
            runSpacing: 2, // Khoảng cách giữa các hàng nếu tràn
            children: scheme.map((item) {
              return Container(
                padding: EdgeInsets.symmetric(vertical: 3, horizontal: 7),
                decoration: BoxDecoration(
                  color: Colors.white, // Màu nền
                  border: Border.all(color: AppColors.main_background, width: 1), // Viền
                  borderRadius: BorderRadius.circular(12), // Bo góc
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2), // Màu bóng
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: Offset(0, 2), // Dịch bóng xuống dưới
                    ),
                  ],
                ),
                child: Text(
                  item, // Nội dung text
                  style: style_S12_W600_BlackColor,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
