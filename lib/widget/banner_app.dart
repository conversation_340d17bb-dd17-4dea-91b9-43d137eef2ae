import 'package:card_promo/app_controller.dart';
import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/res/image/app_images.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class BannerApp extends StatelessWidget {
  const BannerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return TouchableWidget(
      margin: EdgeInsets.symmetric(vertical: 20, horizontal: 10),
      padding: EdgeInsets.zero,
      borderRadiusEffect: BorderRadius.circular(20),
      onPressed: () async {
        // print('print');
        openLink(context);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15, horizontal: 18),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.grayBorder, width: 0.3),
          color: AppColors.white,
          borderRadius: BorderRadius.all(Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: AppColors.gray1.withOpacity(0.3), // Màu đổ bóng nhẹ
              blurRadius: 1, // Độ mờ của bóng
              spreadRadius: 0.2, // Độ lan rộng của bóng
              offset: Offset(1, 1),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Image.asset(AppImages.ic_group_card, width: 80, height: 80,),
            Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 5),
                  child: Column(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                  Text(
                    'Cộng đồng review thẻ',
                    style: style_S18_W600_BlackColor,
                  ),
                  SizedBox(height: 5,),
                  Text(
                    'Tham gia ngay',
                    style: style_S14_W400_GreyColor,
                  ),
                                ],
                              ),
                )),
            Container(
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                  color: AppColors.main_background_light.withOpacity(0.2)),
              child: Icon(
                size: 14,
                Icons.arrow_forward_ios_rounded,
                color: AppColors.main_background,
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> openLink(BuildContext context) async {
    if (!isNullOrEmpty(Get.find<MyAppController>().config.value.linkGroup)) {
      final Uri uri = Uri.parse('${Get.find<MyAppController>().config.value.linkGroup}');
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        Get.snackbar("Lỗi", "Không thể mở đường dẫn!",
            snackPosition: SnackPosition.BOTTOM, backgroundColor: AppColors.red, colorText: Colors.white);
      }
    }

  }
}
