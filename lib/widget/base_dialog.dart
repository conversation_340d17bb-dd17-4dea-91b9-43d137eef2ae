import 'package:card_promo/res/image/app_images.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../res/color/app_colors.dart';
import '../res/string/app_strings.dart';
import '../util/app_dimens.dart';

class BaseDialog extends StatelessWidget {
  final String? type;
  String? pathIcTop;
  final String? title;
  final String? desc;
  final Widget? widgetDesc;
  final String? subDesc;
  final Function? onPress1stButton, onPress2ndButton;
  final String? nameBtn1st, nameBtn2nd;
  final bool? isTwoButton;

  BaseDialog(
      {this.pathIcTop,
      this.title,
      this.desc,
      this.widgetDesc,
      this.subDesc,
      this.onPress1stButton,
      this.onPress2ndButton,
      this.nameBtn1st,
      this.nameBtn2nd,
      this.isTwoButton, this.type});


  static const String TYPE_SUCCESS = "SUCCESS";
  static const String TYPE_ERROR = "ERROR";
  static const String TYPE_WARNING = "WARNING";
  static const String TYPE_CUSTOM = "CUSTOM";

  @override
  Widget build(BuildContext context) {
    initStyleByType();
    return Dialog(
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
        elevation: 0.0,
        backgroundColor: Colors.transparent,
        child: dialogContent(context));
  }

  dialogContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimens.spaceSmall),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(Radius.circular(AppDimens.radiusMedium)),
      ),
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
            pathIcTop!,
            width: 100,
            height: 100,
          ),
          SizedBox(
            height: AppDimens.spaceXSmall10,
          ),
          Text(
            title!,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: AppColors.black,
                fontWeight: FontWeight.bold,
                fontSize: AppDimens.textSizeMedium),
          ),
          SizedBox(
            height: AppDimens.spaceXSmall10,
          ),

          Text('------'),

          SizedBox(
            height: AppDimens.spaceXSmall10,
          ),
          desc == null
              ? SizedBox()
              : Text(
            textAlign: TextAlign.center,
                  desc!,
                  style: TextStyle(
                      color: AppColors.black,
                      fontSize: AppDimens.textSizeSmall),
                ),
          (subDesc == null)
              ? SizedBox()
              : Text(
                  subDesc!,
                  style: TextStyle(
                      color: AppColors.black,
                      fontSize: AppDimens.textSizeSmall),
                ),
          // (widgetDesc??SizedBox()),
          (widgetDesc == null ? SizedBox() : widgetDesc!),
          SizedBox(height: 20,),
          Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: TouchableWidget(
                  height: 45,
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    if (onPress1stButton != null) {
                      onPress1stButton!();
                    }
                    Get.back();
                  },
                  borderRadiusEffect: BorderRadius.circular(10),
                  decoration: BoxDecoration(
                    color: (isTwoButton == true)
                        ? AppColors.mainBackground
                        : AppColors.main_background,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    textAlign: TextAlign.center,
                    nameBtn1st ?? AppStrings.getString(AppStrings.ok)!,
                    style: TextStyle(
                      fontSize: AppDimens.textSizeLarge,
                      fontFamily: kFontFamilyBeVietnamPro,
                      color: (isTwoButton == true)
                          ? AppColors.gray
                          : AppColors.white,
                    ),
                  ),
                ),
              ),
              (isTwoButton == true
                  ? SizedBox(
                      width: AppDimens.spaceXSmall10,
                    )
                  : SizedBox.shrink()),
              (onPress2ndButton == null
                  ? SizedBox.shrink()
                  : Expanded(
                      flex: 1,
                      child: Container(
                        margin: EdgeInsets.only(top: AppDimens.spaceMedium),
                        child: TouchableWidget(
                          onPressed: () => onPress2ndButton,
                          padding: EdgeInsets.zero,
                          borderRadiusEffect: BorderRadius.circular(10),
                          decoration: BoxDecoration(
                            color: AppColors.bgButton,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            nameBtn2nd ??
                                AppStrings.getString(AppStrings.cancel),
                            style: style_S18_W400_BlackColor,
                          ),
                        ),
                      ),
                    )),
            ],
          )
        ],
      ),
    );
  }

  void initStyleByType() {
    pathIcTop= null;
    if (type == TYPE_ERROR) {
      pathIcTop = AppImages.ic_group_card;
    } else if (type == TYPE_WARNING) {
      pathIcTop = AppImages.ic_group_card;
    } else if (type == TYPE_SUCCESS) {
      pathIcTop = AppImages.ic_group_card;
    }
  }
}
