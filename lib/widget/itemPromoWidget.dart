import 'dart:convert';

import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../data/model/promo_detail.dart';
import '../res/color/app_colors.dart';
import '../res/image/app_images.dart';
import '../res/string/app_strings.dart';
import '../util/app_route.dart';
import '../util/app_validation.dart';
import '../util/styles.dart';
import 'bankAndScheme.dart';
import 'line_widget.dart';

class ItemPromoWidget extends StatelessWidget {
  final List<PromoDetail> lsPromoShow;

  const ItemPromoWidget({super.key, required this.lsPromoShow});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        shrinkWrap: true,
        // Important: Allow ListView to be inside a Column
        physics: NeverScrollableScrollPhysics(),
        // Important: Disable
        padding: EdgeInsets.zero,
        itemCount: lsPromoShow.length,
        itemBuilder: (context, index) {
          PromoDetail data = lsPromoShow[index];
          return Container(
            margin: EdgeInsets.symmetric(vertical: 5),
            child: TouchableWidget(
              borderRadiusEffect: BorderRadius.circular(15),
              padding: EdgeInsets.zero,
              onPressed: () {
                Future.delayed(Duration(milliseconds: 100), () {
                  onPressDetailPromo(data);
                });
              },
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: AppColors.lightGreyText,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          isNullEmpty(data.iconBank)
                              ? Icon(Icons.account_balance, color: AppColors.main_background,)
                          : SvgPicture.network(
                                  data.iconBank!,
                                  fit: BoxFit.fill,
                                  width: 35,
                                  height: 35,
                                  placeholderBuilder: (context) => Icon(
                                    Icons.account_balance,
                                    color: AppColors.main_background,
                                  ),
                                ),
                          SizedBox(width: 10,),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${data.title}',
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: style_S16_W600_BlackColor,
                                ),
                                SizedBox(
                                  height: 5,
                                ),
                                BankandschemeWidget(
                                  bank: '',
                                  scheme: data.scheme ?? [],
                                ),
                              ],
                            ),
                          ),
                        ]),
                    SizedBox(
                      height: 5,
                    ),
                    Text(
                      maxLines: 2,
                      '${data.description?.replaceAll('/n', '\n')}',
                      // data.description,
                      overflow: TextOverflow.ellipsis,
                      style: style_S14_W400_BlackColor,
                    ),
                    Container(
                      child: MySeparator(
                        height: 0.5,
                        color: AppColors.gray2,
                      ),
                      padding: EdgeInsets.symmetric(vertical: 5),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text(
                          '${AppStrings.getString(AppStrings.tt_end_date)}: ${data.endDateAsDate}',
                          style: style_S14_W400_BlackColor,
                        ),
                        Container(
                          child: Text(
                            AppStrings.getString(AppStrings.tt_detail),
                            style: style_S14_W600_BlueColor,
                          ),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
          );
        });
  }

  void onPressDetailPromo(PromoDetail promoData) async {
    if (promoData != null) {
      Get.toNamed(
        AppNameRoute.promo_detai_screen,
        arguments: jsonEncode(promoData),
      );
    }
    return;
  }
}
