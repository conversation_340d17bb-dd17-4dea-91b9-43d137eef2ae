import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../app_controller.dart';
import '../data/model/bank_data.dart';
import '../data/model/card_data.dart';
import '../res/color/app_colors.dart';
import '../res/string/app_strings.dart';
import '../util/local_storage.dart';
import '../util/styles.dart';
import 'bankAndScheme.dart';
import 'line_widget.dart';
import 'touchable_widget.dart';

class AddCardWidget extends StatefulWidget {
  List<CardData> listBankSaved;
  List<BankData> listBankOriginal;
  Function onRefresh;

  AddCardWidget(
      {super.key,
      required this.listBankSaved,
      required this.listBankOriginal,
      required this.onRefresh});

  @override
  State<AddCardWidget> createState() => _AddCardWidgetState();
}

class _AddCardWidgetState extends State<AddCardWidget> {
  int tab = 0;
  String searchText = '';

  late BuildContext context;
  List<BankData> listBankShow = [];

  @override
  void initState() {
    super.initState();
    // Initialize with all banks
    listBankShow.addAll(widget.listBankOriginal);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
  }

  void changeTab(int index) {
    setState(() {
      tab = index;
    });
  }

  // Helper function to check if a bank is already saved
  bool isBankSaved(CardData card) {
    return widget.listBankSaved.any((cardData) => (cardData.name == card.name && cardData.pan == card.pan && cardData.scheme == card.scheme));
  }

  // Filter banks based on search text
  void _onFilterBank(String searchValue) {
    setState(() {
      searchText = searchValue;
      if (searchValue.isEmpty) {
        // If search is empty, show all banks
        listBankShow.clear();
        listBankShow.addAll(widget.listBankOriginal);
      } else {
        // Filter banks by bankCode or bankName (case insensitive)
        listBankShow.clear();
        listBankShow.addAll(
          widget.listBankOriginal.where((bank) {
            final searchLower = searchValue.toLowerCase();
            final bankCodeMatch = bank.bankCode?.toLowerCase().contains(searchLower) == true;
            final bankNameMatch = bank.bankName?.toLowerCase().contains(searchLower) == true;
            final shortNameMatch = bank.shortName?.toLowerCase().contains(searchLower) == true;

            return bankCodeMatch || bankNameMatch || shortNameMatch;
          }).toList(),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    setState(() {
      this.context = context;
    });
    return Container(
        height: MediaQuery.of(context).size.height / (5 / 4),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
        decoration: const BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.only(
                topLeft: const Radius.circular(10),
                topRight: const Radius.circular(10))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      TouchableWidget(
                          // padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          decoration: BoxDecoration(
                              color: tab == 0 ? AppColors.main_background : AppColors.lightGreyText,
                              borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          onPressed: () {
                            changeTab(0);
                          },
                          child: Text(
                            'Quản lý thẻ',
                            style: tab == 0 ? style_S12_W600_WhiteColor : style_S12_W600_BlackColor,
                          )),
                      SizedBox(width: 5,),
                      TouchableWidget(
                          // padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          decoration: BoxDecoration(
                            color: tab == 1 ? AppColors.main_background : AppColors.lightGreyText,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                          ),
                          onPressed: () {
                            changeTab(1);
                          },
                          child: Text(
                            'Thêm thẻ',
                            style: tab == 0 ? style_S12_W600_BlackColor : style_S12_W600_WhiteColor,
                          )),

                    ],
                  ),
                ),
                TouchableWidget(
                    onPressed: () {
                      Get.back();
                    },
                    child: Icon(
                      Icons.close,
                    ))
              ],
            ),
            SizedBox(height: 10),
            Divider(
              color: AppColors.gray,
              // Đường kẻ phân cách
              thickness: 0.5,
              height: 0,
            ),
            tab == 0 ? _buildManagerCard() : _buildAddCard(),
          ],
        ));
  }

  void saveBankCard(BankData bank) async {
    String digitPan = '';
    String selectedCardType = 'Visa'; // Default selection

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        SvgPicture.network(
                          '${bank.icon}',
                          width: 40,
                          height: 40,
                          placeholderBuilder: (context) => Icon(
                            Icons.account_balance,
                            color: AppColors.primary,
                            size: 40,
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Thêm thẻ ${bank.bankCode}',
                                style: style_S16_W600_BlackColor,
                              ),
                              Text(
                                '${bank.bankName}',
                                style: style_S12_W400_BlackColor,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),

                    // Input 6 số đầu thẻ
                    Text(
                      'Nhập 6 số đầu thẻ',
                      style: style_S14_W600_BlackColor,
                    ),
                    SizedBox(height: 4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.gray),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextField(
                        keyboardType: TextInputType.number,
                        maxLength: 6,
                        decoration: InputDecoration(
                          hintText: 'Ví dụ: 123456',
                          hintStyle: style_S14_W400_BlackColor.copyWith(
                            color: AppColors.gray,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                          counterText: '',
                        ),
                        onChanged: (value) {
                          digitPan = value;
                        },
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Lưu ý: Khuyến mãi sẽ hiển thị dựa trên số thẻ bạn cung cấp',
                      style: style_S12_W600_WarningColor,
                    ),
                    SizedBox(height: 10),

                    // Chọn loại thẻ
                    Text(
                      'Loại thẻ',
                      style: style_S14_W600_BlackColor,
                    ),
                    SizedBox(height: 10),
                    Row(
                      // mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildCardTypeOption(
                          'Visa',
                          selectedCardType,
                          (value) {
                            setDialogState(() {
                              selectedCardType = value;
                            });
                          },
                        ),
                        SizedBox(width: 10),
                        _buildCardTypeOption(
                          'Master',
                          selectedCardType,
                          (value) {
                            setDialogState(() {
                              selectedCardType = value;
                            });
                          },
                        ),
                        SizedBox(width: 10),
                        _buildCardTypeOption(
                          'Napas',
                          selectedCardType,
                          (value) {
                            setDialogState(() {
                              selectedCardType = value;
                            });
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 15),
                    // Buttons
                    Divider(
                      color: AppColors.gray2,
                      // Đường kẻ phân cách
                      thickness: 0.5,
                      height: 0.5,
                    ),
                    SizedBox(height: 10),
                    Row(
                      children: [
                        Expanded(
                          child: TouchableWidget(
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.lightGreyText),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              'Hủy',
                              style: style_S14_W600_BlackColor,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        SizedBox(width: 10),
                        Expanded(
                          child: TouchableWidget(
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.lightGreyText),
                              borderRadius: BorderRadius.circular(8),
                              color: AppColors.main_background
                            ),
                            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                            onPressed: () {
                              if (digitPan.length == 6) {
                                Navigator.pop(context);
                                CardData card = CardData(
                                  name: bank.bankCode,
                                  icon: bank.icon,
                                  pan: digitPan,
                                  scheme: selectedCardType,
                                );
                                doSaveBankCard(card);
                              } else {
                                // Show error
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Vui lòng nhập đủ 6 số đầu thẻ'),
                                    backgroundColor: AppColors.redText,
                                  ),
                                );
                              }
                            },
                            child: Text(
                              'Thêm thẻ',
                              style: style_S14_W600_WhiteColor,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildCardTypeOption(
    String cardType,
    String selectedType,
    Function(String) onSelected,
  ) {
    bool isSelected = cardType == selectedType;
    return TouchableWidget(
      padding: EdgeInsets.zero,
      onPressed: () => onSelected(cardType),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.main_background : AppColors.white,
          border: Border.all(
            color: isSelected ? AppColors.main_background : AppColors.gray,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          cardType,
          style: isSelected
              ? style_S14_W600_WhiteColor
              : style_S14_W600_BlackColor,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  void doSaveBankCard(CardData card) async {
    if (!isBankSaved(card)) {
      setState(() {
        widget.listBankSaved.add(card);
      });
      await LocalStorage().saveBankCard(card);
      widget.onRefresh();
      Get.back();
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã thêm thẻ ${card.name} thành công'),
          backgroundColor: AppColors.main_background,
        ),
      );
    }
  }

  void removeBankCard(CardData card) async {
    if (isBankSaved(card)) {
      setState(() {
        // Remove the CardData that matches the bank.bankCode
        widget.listBankSaved.removeWhere((cardData) => cardData.name == card.name);
      });
      await LocalStorage().removeBankCard(card);
      widget.onRefresh();
    }
  }

  _buildManagerCard() {
    // Bỏ phần tử đầu tiên trước khi hiển thị
    List<CardData> displayList = widget.listBankSaved.length > 1
        ? widget.listBankSaved.skip(1).toList()
        : [];

    return Expanded(
      child: displayList.isEmpty
          ? _buildEmptyManagerState()
          : Column(
              children: [
                // Header với số lượng thẻ
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: AppColors.main_background.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.main_background.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.credit_card,
                        color: AppColors.main_background,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Danh sách thẻ đã lưu (${displayList.length})',
                        style: style_S14_W600_BlackColor.copyWith(
                          color: AppColors.main_background,
                        ),
                      ),
                    ],
                  ),
                ),
                // Danh sách thẻ
                Expanded(
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    itemCount: displayList.length,
                    itemBuilder: (context, index) {
                      CardData card = displayList[index];
                      return Container(
                        margin: EdgeInsets.only(bottom: 10),
                        decoration: BoxDecoration(
                          color: AppColors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.gray.withOpacity(0.1),
                              blurRadius: 8,
                              offset: Offset(0, 2),
                            ),
                          ],
                          border: Border.all(
                            color: AppColors.gray.withOpacity(0.2),
                          ),
                        ),
                        child: Column(
                          children: [
                            ListTile(
                              // contentPadding: EdgeInsets.all(16),
                              leading: Container(
                                width: 50,
                                height: 50,
                                decoration: BoxDecoration(
                                  color: AppColors.grayBackground,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: SvgPicture.network(
                                  '${card.icon}',
                                  fit: BoxFit.contain,
                                  width: 30,
                                  height: 30,
                                  placeholderBuilder: (context) => Icon(
                                    Icons.account_balance,
                                    color: AppColors.primary,
                                    size: 24,
                                  ),
                                ),
                              ),
                              title: BankandschemeWidget(bank: card.name ?? '', scheme: [card.scheme ?? '']),
                              subtitle: Text(
                                'PAN: ${_buildPan(card.pan) ?? '**** **'}** **** ****',
                                style: style_S12_W400_BlackColor.copyWith(
                                  color: AppColors.gray,
                                ),
                              ),
                            ),
                            // Action buttons
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                              decoration: BoxDecoration(
                                color: AppColors.grayBackground.withOpacity(0.5),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(12),
                                  bottomRight: Radius.circular(12),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: TouchableWidget(
                                      padding: EdgeInsets.zero,
                                      onPressed: () => _showUpdateCardDialog(card),
                                      child: Container(
                                        padding: EdgeInsets.symmetric(vertical: 10),
                                        decoration: BoxDecoration(
                                          color: AppColors.main_background,
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.edit,
                                              color: AppColors.white,
                                              size: 16,
                                            ),
                                            SizedBox(width: 6),
                                            Text(
                                              'Cập nhật',
                                              style: style_S14_W600_WhiteColor,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 12),
                                  Expanded(
                                    child: TouchableWidget(
                                      padding: EdgeInsets.zero,
                                      onPressed: () => _showDeleteCardDialog(card),
                                      child: Container(
                                        padding: EdgeInsets.symmetric(vertical: 10),
                                        decoration: BoxDecoration(
                                          color: AppColors.redText,
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.delete_outline,
                                              color: AppColors.white,
                                              size: 16,
                                            ),
                                            SizedBox(width: 6),
                                            Text(
                                              'Xóa',
                                              style: style_S14_W600_WhiteColor,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildEmptyManagerState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.credit_card_off,
            size: 64,
            color: AppColors.gray,
          ),
          SizedBox(height: 16),
          Text(
            'Chưa có thẻ nào được lưu',
            style: style_S16_W600_BlackColor.copyWith(
              color: AppColors.gray,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Hãy thêm thẻ từ tab "Thêm thẻ"',
            style: style_S14_W400_BlackColor.copyWith(
              color: AppColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getSchemeColor(String scheme) {
    switch (scheme.toLowerCase()) {
      case 'VISA':
        return Color(0xFF1A1F71);
      case 'MASTER':
        return Color(0xFFEB001B);
      case 'NAPAS':
        return Color(0xFF00A651);
      default:
        return AppColors.gray;
    }
  }

  void _showUpdateCardDialog(CardData card) {
    String selectedCardType = card.scheme ?? 'Visa';
    String digitPan = card.pan ?? '';

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppColors.grayBackground,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: SvgPicture.network(
                            '${card.icon}',
                            width: 30,
                            height: 30,
                            placeholderBuilder: (context) => Icon(
                              Icons.account_balance,
                              color: AppColors.primary,
                              size: 24,
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Cập nhật thẻ ${card.name}',
                                style: style_S16_W600_BlackColor,
                              ),
                              Text(
                                'Thay đổi loại thẻ',
                                style: style_S12_W400_BlackColor.copyWith(
                                  color: AppColors.gray,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20),

                    // Input 6 số đầu thẻ
                    Text(
                      'Nhập 6 số đầu thẻ',
                      style: style_S14_W600_BlackColor,
                    ),
                    SizedBox(height: 4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.gray),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextField(
                        controller: TextEditingController()..text = digitPan,
                        keyboardType: TextInputType.number,
                        maxLength: 6,
                        decoration: InputDecoration(
                          hintText: 'Ví dụ: 123456',
                          hintStyle: style_S14_W400_BlackColor.copyWith(
                            color: AppColors.gray,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                          counterText: '',
                        ),
                        onChanged: (value) {
                          digitPan = value;
                          card.pan = digitPan;
                        },
                      ),
                    ),
                    SizedBox(height: 8),
                    SizedBox(height: 16),

                    // Chọn loại thẻ mới
                    Text(
                      'Chọn loại thẻ mới',
                      style: style_S14_W600_BlackColor,
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        _buildCardTypeOption(
                          'Visa',
                          selectedCardType,
                          (value) {
                            setDialogState(() {
                              selectedCardType = value;
                              card.scheme = selectedCardType;
                            });
                          },
                        ),
                        SizedBox(width: 12),
                        _buildCardTypeOption(
                          'Master',
                          selectedCardType,
                          (value) {
                            setDialogState(() {
                              selectedCardType = value;
                              card.scheme = selectedCardType;
                            });
                          },
                        ),
                        SizedBox(width: 12),
                        _buildCardTypeOption(
                          'Napas',
                          selectedCardType,
                          (value) {
                            setDialogState(() {
                              selectedCardType = value;
                              card.scheme = selectedCardType;
                            });
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 15),
                    Divider(
                      color: AppColors.gray2,
                      // Đường kẻ phân cách
                      thickness: 0.5,
                      height: 0.5,
                    ),
                    // Buttons
                    SizedBox(height: 10),
                    Row(
                      children: [
                        Expanded(
                          child: TouchableWidget(
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.gray),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            onPressed: () => Navigator.pop(context),
                            child: Text(
                              'Hủy',
                              style: style_S14_W600_BlackColor,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: TouchableWidget(
                            decoration: BoxDecoration(
                              color: AppColors.main_background,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            onPressed: () {
                              Navigator.pop(context);
                              _doUpdateCard(card);
                            },
                            child: Text(
                              'Cập nhật',
                              style: style_S14_W600_WhiteColor,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  void _showDeleteCardDialog(CardData card) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Container(
            padding: EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon cảnh báo
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: AppColors.redText.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    Icons.warning_amber_rounded,
                    color: AppColors.redText,
                    size: 30,
                  ),
                ),
                SizedBox(height: 16),

                // Tiêu đề
                Text(
                  'Xóa thẻ khỏi danh sách',
                  style: style_S18_W600_BlackColor,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 8),

                // Nội dung
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: style_S14_W400_BlackColor,
                    children: [
                      TextSpan(text: 'Bạn có chắc chắn muốn xóa thẻ '),
                      TextSpan(
                        text: '${card.name}',
                        style: style_S14_W600_BlackColor.copyWith(
                          color: AppColors.main_background,
                        ),
                      ),
                      TextSpan(text: ' khỏi danh sách?'),
                    ],
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Hành động này không thể hoàn tác.',
                  style: style_S12_W400_BlackColor.copyWith(
                    color: AppColors.gray,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: TouchableWidget(
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.gray),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        onPressed: () => Navigator.pop(context),
                        child: Text(
                          'Hủy',
                          style: style_S14_W600_BlackColor,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: TouchableWidget(
                        decoration: BoxDecoration(
                          color: AppColors.redText,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        onPressed: () {
                          Navigator.pop(context);
                          _doDeleteCard(card);
                        },
                        child: Text(
                          'Xóa',
                          style: style_S14_W600_WhiteColor,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _doUpdateCard(CardData card) async {
    // Tìm và cập nhật thẻ trong danh sách
    int index = widget.listBankSaved.indexWhere((c) => c.name == card.name);
    if (index != -1) {
      setState(() {
        widget.listBankSaved[index] = card;
      });

      await LocalStorage().removeBankCard(card);
      await LocalStorage().saveBankCard(card);

      widget.onRefresh();

      // Hiển thị thông báo thành công
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đã cập nhật thẻ ${card.name} thành công'),
          backgroundColor: AppColors.main_background,
        ),
      );
    }
  }

  void _doDeleteCard(CardData card) async {
    // Xóa thẻ khỏi danh sách
    setState(() {
      widget.listBankSaved.removeWhere((c) => c.name == card.name);
    });
    await LocalStorage().removeBankCard(card);

    widget.onRefresh();

    // Hiển thị thông báo thành công
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã xóa thẻ ${card.name} thành công'),
        backgroundColor: AppColors.redText,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.gray,
          ),
          SizedBox(height: 16),
          Text(
            'Không tìm thấy ngân hàng',
            style: style_S16_W600_BlackColor.copyWith(
              color: AppColors.gray,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Thử tìm kiếm với từ khóa khác',
            style: style_S14_W400_BlackColor.copyWith(
              color: AppColors.gray,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Helper method to highlight search text
  Widget _buildHighlightedText(String text, TextStyle style) {
    if (searchText.isEmpty) {
      return Text(text, style: style);
    }

    final searchLower = searchText.toLowerCase();
    final textLower = text.toLowerCase();

    if (!textLower.contains(searchLower)) {
      return Text(text, style: style);
    }

    final startIndex = textLower.indexOf(searchLower);
    final endIndex = startIndex + searchText.length;

    return RichText(
      text: TextSpan(
        children: [
          if (startIndex > 0)
            TextSpan(
              text: text.substring(0, startIndex),
              style: style,
            ),
          TextSpan(
            text: text.substring(startIndex, endIndex),
            style: style.copyWith(
              backgroundColor: AppColors.main_background.withOpacity(0.3),
              fontWeight: FontWeight.bold,
            ),
          ),
          if (endIndex < text.length)
            TextSpan(
              text: text.substring(endIndex),
              style: style,
            ),
        ],
      ),
    );
  }

  _buildAddCard() {
    return Expanded(
      child: Column(
        children: [
          // Search TextField
          Container(
            height: 35,
            margin: EdgeInsets.symmetric(horizontal: 5, vertical: 5),
            decoration: BoxDecoration(
              color: AppColors.grayBackground,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: AppColors.gray.withOpacity(0.3)),
            ),
            child: TextField(
              keyboardType: TextInputType.text,
              decoration: InputDecoration(
                hintText: 'Tìm kiếm ngân hàng (VD: VCB, ACB, TCB...)',
                hintStyle: style_S14_W400_BlackColor.copyWith(
                  color: AppColors.gray,
                ),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.gray,
                  size: 15,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 10,
                ),
              ),
              onChanged: (value) {
                _onFilterBank(value);
              },
            ),
          ),
          // Results count
          if (listBankShow.isNotEmpty)
            Container(
              margin: EdgeInsets.symmetric(horizontal: 10),
              alignment: Alignment.centerLeft,
              child: Text(
                'Tìm thấy ${listBankShow.length} ngân hàng',
                style: style_S12_W400_BlackColor.copyWith(
                  color: AppColors.gray,
                ),
              ),
            ),
          SizedBox(height: 8),
          Expanded(
            child: listBankShow.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount: listBankShow.length,
                    itemBuilder: (context, index) {
                      BankData bank = listBankShow[index];
                      return TouchableWidget(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            saveBankCard(bank);
                          },
                      child: Column(
                        children: [
                          ListTile(
                              minLeadingWidth: 5,
                              minTileHeight: 5,
                              minVerticalPadding: 5,
                              contentPadding:
                                  EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                              leading: SvgPicture.network(
                                '${bank.icon}',
                                fit: BoxFit.contain,
                                width: 30,
                                height: 30,
                                placeholderBuilder: (context) => Icon(
                                  Icons.account_balance,
                                  color: AppColors.primary,
                                  size: 20,
                                ),
                              ),
                              title: _buildHighlightedText(
                                '${bank.bankCode}',
                                style_S14_W600_BlackColor,
                              ),
                              subtitle: _buildHighlightedText(
                                '${bank.bankName}',
                                style_S12_W400_BlackColor,
                              ),
                              trailing: Container(
                                width: 70,
                                padding:
                                    EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                                decoration: BoxDecoration(
                                    color: AppColors.main_background,
                                    borderRadius: BorderRadius.circular(10)),
                                child: Text(
                                  // '${isBankSaved(bank) ? '${AppStrings.getString(AppStrings.tt_delete)} -' : '${AppStrings.getString(AppStrings.tt_add)} +'}',
                                  '${AppStrings.getString(AppStrings.tt_add)}',
                                  style: style_S14_W400_WhiteColor,
                                  textAlign: TextAlign.center,
                                ),
                              ) // Icon hướng dẫn click
                              ),
                          Divider(
                            color: AppColors.grayBackground,
                            // Đường kẻ phân cách
                            thickness: 0.5,
                            height: 0.5,
                          ),
                        ],
                      ));
                }),
          ),
        ],
      ),
    );
  }

  String _buildPan(String? pan) {
    if (pan == null || pan.isEmpty) {
      return '**** **';
    }

    // Đảm bảo chỉ lấy tối đa 6 ký tự
    String cleanPan = pan.replaceAll(RegExp(r'[^0-9]'), ''); // Chỉ lấy số
    if (cleanPan.length > 6) {
      cleanPan = cleanPan.substring(0, 6);
    }

    // Pad với * nếu thiếu ký tự
    String paddedPan = cleanPan.padRight(6, '*');

    // Format thành **** **
    String formatted = '${paddedPan.substring(0, 4)} ${paddedPan.substring(4, 6)}';

    return formatted;
  }

}
