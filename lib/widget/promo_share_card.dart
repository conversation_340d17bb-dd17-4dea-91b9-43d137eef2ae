import 'dart:io';
import 'dart:ui';
import 'package:card_promo/res/color/app_colors.dart';
import 'package:card_promo/res/image/app_images.dart';
import 'package:card_promo/util/app_validation.dart';
import 'package:card_promo/util/styles.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

import '../data/model/promo_detail.dart';

class PromoShareCard extends StatefulWidget {
  final PromoDetail promo;

  const PromoShareCard({Key? key, required this.promo}) : super(key: key);

  @override
  _PromoShareCardState createState() => _PromoShareCardState();
}

class _PromoShareCardState extends State<PromoShareCard> {
  final ScreenshotController _screenshotController = ScreenshotController();

  void _sharePromo() async {
    final image = await _screenshotController.capture();
    if (image != null) {
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/promo.png').writeAsBytes(image);
      // await Share.shareFiles([file.path], text: "${widget.promo.title}\n${widget.promo.linkPromo}");
      await Share.shareXFiles([XFile('${file.path}')],
          text: "${widget.promo.title}\n${widget.promo.linkPromo}");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20), // Bo góc 20px
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Container(
            decoration: BoxDecoration(
              color: AppColors.transparent,
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(20), topLeft: Radius.circular(20)),
              // boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 6)],
            ),
            height: MediaQuery.of(context).size.height / (3 / 2),
            child: Column(
              children: [
                Expanded(
                  child: Screenshot(
                      controller: _screenshotController,
                      child: Container(
                        color: AppColors.white,
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: Opacity(
                                opacity: 0.3, // Điều chỉnh độ mờ (0.0 -> 1.0)
                                child: Image.asset(
                                  AppImages.background_share_wg,
                                  fit: BoxFit.cover, // Phủ toàn bộ Container
                                ),
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: AppColors.transparent,
                                borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(20),
                                    topLeft: Radius.circular(20)),
                                // boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 6)],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                      child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      (widget.promo.promoImageUrl != null &&
                                              widget.promo.promoImageUrl!
                                                  .isNotEmpty)
                                          ? ClipRRect(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              child: Image.network(
                                                widget.promo.promoImageUrl!,
                                                height: 150,
                                                fit: BoxFit.cover,
                                                errorBuilder: (context, error,
                                                        stackTrace) =>
                                                    Image.asset(
                                                  AppImages.ic_group_card,
                                                  height: 150,
                                                ),
                                              ),
                                            )
                                          : Image.asset(
                                              AppImages.play_store_512,
                                              height: MediaQuery.of(context).size.height/7,
                                            ),
                                      SizedBox(height: 10),
                                      Text(
                                        widget.promo.title ?? "Khuyến mãi",
                                        style: style_S18_W600_BlackColor,
                                        textAlign: TextAlign.center,
                                      ),
                                      SizedBox(height: 6),
                                      isNullOrEmpty(widget.promo.promoCode) ? SizedBox.shrink() :
                                      Container(
                                        margin:
                                            EdgeInsets.symmetric(vertical: 5),
                                        child: (isNullEmpty(
                                                widget.promo.promoCode))
                                            ? SizedBox.shrink()
                                            : Container(
                                                padding: EdgeInsets.all(10),
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                10)),
                                                    color: AppColors
                                                        .ufo_color_background),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                      "Mã: ",
                                                      style:
                                                          style_S14_W400_BlackColor,
                                                    ),
                                                    Text(
                                                      "${widget.promo.promoCode}",
                                                      style:
                                                          style_S20_W600_BlackColor,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 5, vertical: 5),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(
                                                  Icons.store,
                                                  color: AppColors.main_background,
                                                ),
                                                SizedBox(
                                                  width: 5,
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    "${widget.promo.merchantName}",
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis,
                                                    style: style_S14_W600_BlackColor,
                                                  ),
                                                )
                                              ],
                                            ),
                                            SizedBox(
                                              height: 5,
                                            ),
                                            isNullOrEmpty(widget.promo.storeLocation) ? SizedBox.shrink() :
                                            Text(
                                                '${widget.promo.storeLocation}')
                                          ],
                                        ),
                                      ),
                                      Expanded(
                                        child: SingleChildScrollView(
                                          padding: EdgeInsets.zero,
                                          child: Expanded(
                                            child: Text(
                                                '${widget.promo.description?.replaceAll('/n', '\n')}',
                                                textAlign: TextAlign.center,
                                                // maxLines: 5,
                                                // overflow: TextOverflow.ellipsis,
                                                style: TextStyle(fontSize: 14)),
                                          ),
                                        ),
                                      ),
                                    ],
                                  )),
                                ],
                              ),
                            ),
                            Container(
                              alignment: Alignment.topRight,
                              child: TouchableWidget(
                                padding: EdgeInsets.zero,
                                width: 35,
                                height: 35,
                                margin: EdgeInsets.only(
                                    top: 10, right: 20),
                                // padding: EdgeInsets.all(5),
                                decoration: BoxDecoration(
                                  color: AppColors.lightGreyText,
                                  borderRadius: BorderRadius.all(Radius.circular(20)),
                                ),
                                onPressed: () => Navigator.pop(context),
                                child: Center(
                                    child: Icon(
                                      Icons.close,
                                      color: AppColors.white,
                                      size: 20,
                                    )),
                              ),
                            )
                          ],
                        ),
                      )),
                ),
                TouchableWidget(
                    decoration: BoxDecoration(
                        color: AppColors.main_background,
                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
                    onPressed: _sharePromo,
                    child: Text(
                      "Chia sẻ",
                      style: style_S14_W600_WhiteColor,
                    )),
              ],
            )),

      ),
    );
  }
}