import 'package:card_promo/util/styles.dart';
import 'package:flutter/material.dart';
import 'package:text_scroll/text_scroll.dart';

class ScrollingText extends StatelessWidget {
  final String text;
  final TextStyle? style;

  const ScrollingText({Key? key, required this.text, this.style}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextScroll(
      text,
      style: style ?? style_S14_W400_BlackColor,
      velocity: Velocity(pixelsPerSecond: Offset(30, 0)),
      delayBefore: Duration(seconds: 1),
      pauseBetween: Duration(seconds: 3),
      mode: TextScrollMode.endless,
      // <PERSON><PERSON><PERSON> cần fade out ở cả 2 đầu, có thể tùy chỉnh marginFade
      // marginFade: 16,
    );
  }
}
