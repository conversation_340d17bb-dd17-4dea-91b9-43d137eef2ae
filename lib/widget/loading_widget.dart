import 'package:flutter/material.dart';
import '../res/color/app_colors.dart';
import '../util/styles.dart';

class LoadingWidget extends StatelessWidget {
  final String? message;
  final bool showMessage;
  final Color? backgroundColor;
  final Color? progressColor;
  final double? size;
  final LoadingStyle style;

  const LoadingWidget({
    Key? key,
    this.message,
    this.showMessage = true,
    this.backgroundColor,
    this.progressColor,
    this.size,
    this.style = LoadingStyle.card,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    switch (style) {
      case LoadingStyle.card:
        return _buildCardStyle();
      case LoadingStyle.overlay:
        return _buildOverlayStyle();
      case LoadingStyle.inline:
        return _buildInlineStyle();
      case LoadingStyle.minimal:
        return _buildMinimalStyle();
    }
  }

  Widget _buildCardStyle() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            backgroundColor ?? AppColors.main_background,
            backgroundColor?.withOpacity(0.8) ?? AppColors.main_background_light,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: (backgroundColor ?? AppColors.main_background).withOpacity(0.3),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: size ?? 20,
            height: size ?? 20,
            child: CircularProgressIndicator(
              strokeWidth: 2.5,
              valueColor: AlwaysStoppedAnimation<Color>(
                progressColor ?? AppColors.white,
              ),
            ),
          ),
          if (showMessage) ...[
            SizedBox(width: 12),
            Text(
              message ?? 'Đang tải dữ liệu...',
              style: style_S14_W600_WhiteColor,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOverlayStyle() {
    return Container(
      color: AppColors.backDrop,
      child: Center(
        child: Container(
          padding: EdgeInsets.all(32),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.black.withOpacity(0.1),
                blurRadius: 16,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 48,
                height: 48,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    progressColor ?? AppColors.main_background,
                  ),
                ),
              ),
              if (showMessage) ...[
                SizedBox(height: 16),
                Text(
                  message ?? 'Đang tải dữ liệu...',
                  style: style_S16_W600_BlackColor,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInlineStyle() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: size ?? 16,
            height: size ?? 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                progressColor ?? AppColors.main_background,
              ),
            ),
          ),
          if (showMessage) ...[
            SizedBox(width: 8),
            Text(
              message ?? 'Đang tải...',
              style: style_S14_W400_BlackColor.copyWith(
                color: progressColor ?? AppColors.main_background,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMinimalStyle() {
    return Center(
      child: Container(
        width: size ?? 24,
        height: size ?? 24,
        child: CircularProgressIndicator(
          strokeWidth: 2.5,
          valueColor: AlwaysStoppedAnimation<Color>(
            progressColor ?? AppColors.main_background,
          ),
        ),
      ),
    );
  }
}

enum LoadingStyle {
  card,     // Card style with gradient background
  overlay,  // Full screen overlay
  inline,   // Inline with content
  minimal,  // Just the spinner
}

// Shimmer Loading Widget for skeleton screens
class ShimmerLoading extends StatefulWidget {
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;

  const ShimmerLoading({
    Key? key,
    required this.child,
    this.baseColor,
    this.highlightColor,
  }) : super(key: key);

  @override
  _ShimmerLoadingState createState() => _ShimmerLoadingState();
}

class _ShimmerLoadingState extends State<ShimmerLoading>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();
    _animation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _controller, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                widget.baseColor ?? AppColors.grayBackground,
                widget.highlightColor ?? AppColors.white,
                widget.baseColor ?? AppColors.grayBackground,
              ],
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

// Skeleton widgets for common use cases
class SkeletonContainer extends StatelessWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const SkeletonContainer({
    Key? key,
    this.width,
    this.height,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: AppColors.grayBackground,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
    );
  }
}

class SkeletonText extends StatelessWidget {
  final double? width;
  final double height;

  const SkeletonText({
    Key? key,
    this.width,
    this.height = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SkeletonContainer(
      width: width,
      height: height,
      borderRadius: BorderRadius.circular(height / 2),
    );
  }
}

// Specialized loading widgets
class PromoCardSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 5),
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: AppColors.lightGreyText),
      ),
      child: ShimmerLoading(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SkeletonContainer(width: 35, height: 35, borderRadius: BorderRadius.circular(8)),
                SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SkeletonText(width: double.infinity, height: 16),
                      SizedBox(height: 5),
                      SkeletonText(width: 120, height: 12),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            SkeletonText(width: double.infinity, height: 14),
            SizedBox(height: 5),
            SkeletonText(width: 200, height: 14),
            SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SkeletonText(width: 100, height: 12),
                SkeletonText(width: 60, height: 12),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class MerchantCardSkeleton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.grayBorder),
      ),
      child: ShimmerLoading(
        child: Row(
          children: [
            SkeletonContainer(width: 48, height: 48, borderRadius: BorderRadius.circular(12)),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SkeletonText(width: double.infinity, height: 16),
                  SizedBox(height: 4),
                  SkeletonText(width: 150, height: 14),
                ],
              ),
            ),
            SkeletonContainer(width: 16, height: 16, borderRadius: BorderRadius.circular(8)),
          ],
        ),
      ),
    );
  }
}

// Loading states for different screens
class LoadingStates {
  static Widget dataLoading({String? message}) {
    return LoadingWidget(
      message: message ?? 'Đang tải dữ liệu...',
      style: LoadingStyle.card,
    );
  }

  static Widget searchLoading() {
    return LoadingWidget(
      message: 'Đang tìm kiếm...',
      style: LoadingStyle.inline,
      size: 16,
    );
  }

  static Widget fullScreenLoading({String? message}) {
    return LoadingWidget(
      message: message ?? 'Đang tải...',
      style: LoadingStyle.overlay,
    );
  }

  static Widget buttonLoading() {
    return LoadingWidget(
      showMessage: false,
      style: LoadingStyle.minimal,
      size: 20,
      progressColor: AppColors.white,
    );
  }

  static Widget listLoading({int itemCount = 3}) {
    return Column(
      children: List.generate(
        itemCount,
        (index) => PromoCardSkeleton(),
      ),
    );
  }

  static Widget merchantListLoading({int itemCount = 5}) {
    return Column(
      children: List.generate(
        itemCount,
        (index) => MerchantCardSkeleton(),
      ),
    );
  }
}
