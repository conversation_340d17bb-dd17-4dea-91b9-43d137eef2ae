import 'package:card_promo/data/model/promo_detail.dart';
import 'package:card_promo/widget/touchable_widget.dart';
import 'package:flutter/material.dart';

import '../data/model/merchant.dart';
import '../data/model/promo_nearby_merchant.dart';
import '../res/color/app_colors.dart';
import '../util/styles.dart';

class MerchantListNearMeWidget extends StatefulWidget {
  const MerchantListNearMeWidget({
    Key? key,
    required this.callback,
    required this.onDismiss,
    required this.promoNearMc,
  }) : super(key: key);

  final Function callback;
  final VoidCallback onDismiss;
  final List<PromoNearbyMerchant> promoNearMc;

  @override
  State<MerchantListNearMeWidget> createState() => _MerchantListNearMeWidgetState();
}

class _MerchantListNearMeWidgetState extends State<MerchantListNearMeWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          SizedBox(height: 8),
          _buildDragHandle(),
          Expanded(
            child: widget.promoNearMc.length > 0 ? ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 8),
              itemCount: widget.promoNearMc.length,
              itemBuilder: (context, index) {
                final promo = widget.promoNearMc[index];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: promo.nearbyLocations.map((location) {
                    return TouchableWidget(
                      margin: EdgeInsets.symmetric(vertical: 3, horizontal: 10),
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 8,
                            offset: Offset(0, 4),
                          ),
                        ],
                      ),
                      onPressed: () => widget.callback(location, promo.promotions),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.storefront,
                            size: 32,
                            color: AppColors.main_background,
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${location.branchName}',
                                  style: style_S16_W600_BlackColor,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  '${location.address}',
                                  style: style_S14_W400_BlackColor.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          Icon(
                            Icons.chevron_right,
                            color: Colors.grey[400],
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                );
              },
            ) : Center(child: Text('Đang cập nhật khuyến mãi khu vực này!', style: style_S20_W400_BlackColor,),),
          ),
        ],
      ),
    );
  }

  Widget _buildDragHandle() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: Column(
        children: [
          Container(
            width: 36,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          SizedBox(width: 12),
          Row(
            children: [
              Expanded(
                child: Text(
                  'Khuyến mãi gần bạn',
                  style: style_S18_W600_BlackColor,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              widget.promoNearMc.length > 0 ?
              TouchableWidget(
                padding: EdgeInsets.zero,
                width: 35,
                height: 35,
                // padding: EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: AppColors.lightGreyText,
                  borderRadius: BorderRadius.all(Radius.circular(20)),
                ),
                onPressed: () => widget.onDismiss(),
                child: Center(
                    child: Icon(
                      Icons.close,
                      color: AppColors.white,
                      size: 20,
                    )),
              ) : SizedBox.shrink(),
            ],
          ),
        ],
      ),
    );
  }
}
