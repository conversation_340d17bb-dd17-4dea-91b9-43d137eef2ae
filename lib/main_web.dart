import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:flutter/foundation.dart';

import 'app_binding.dart';
import 'build_constants.dart';
import 'res/string/app_strings.dart';
import 'root_screen.dart';
import 'util/app_route.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  BuildConstants.setEnvironment(Environment.DEV);

  runApp(GetMaterialApp(
    debugShowCheckedModeBanner: false,
    initialBinding: AppBinding(),
    initialRoute: AppNameRoute.portalScreen,
    getPages: AppRoute.pages,
    localizationsDelegates: const [
      GlobalMaterialLocalizations.delegate,
      GlobalWidgetsLocalizations.delegate,
      GlobalCupertinoLocalizations.delegate,
    ],
    translations: AppStrings(),
    supportedLocales: [const Locale('vi', 'VN'), const Locale('en', 'US')],
    locale: const Locale('vi', 'VN'),
    fallbackLocale: const Locale('vi', 'VN'),
    builder: (context, child) {
      return RootScreen(child);
    },
  ));
}
