# card_promo

A new Flutter project.

## Getting Started


## Idea
  - Thêm tìm kiếm theo cửa hàng
- <PERSON><PERSON>h năng chia sẻ, lưu yêu thích promo
  - 
- <PERSON><PERSON>, <PERSON><PERSON><PERSON> hình bảng tin như facebook chia sẻ km 



## TODO
- load banner local -> load banner network


## Prompt 
_- 1. ứng dụng tổng hợp các khuyến mãi của các thẻ ngân hàng trên thị trường. người dùng truy cập app và xem các khuyến mãi phù hợp với nhu cầu của mình, ứng dụng chuyển đến trang km, hiển thị code km khi người dùng click chọn sử dụng khuyến mãi này._

- 2. backend của ứng dụng hiện tại có các service sau: gateway, auth - quản lý token, user - quản lý user, thẻ c<PERSON><PERSON> kh<PERSON>, promo - cung cấp thông tin khuyến mãi, noti - bắn noti về khi có km mới.

- 3. Business của ứng dụng: 
- Quảng cáo trong app: Người dùng click sử dụng promo thì show quảng cáo.
- Các thương hiệu hợp tác để hiển thị quảng cáo. 
- Liên kết với công ty trung gian thanh toán có máy pos, khi sử dụng máy pos mà nhập km thì được giảm giá.

Câu hỏi: 
- thẻ của khách có thể cache bên dưới app, server ko lưu thẻ để tránh policy -> user service có cần thiết không?
- Có cần bắt buôộc khách login không? vì app này có thể ko login, khách sử dụng promo thì show quảng cáo mình vẫn có tiền quảng cáo
- Backend có cần thay đổi kiến trúc không?

